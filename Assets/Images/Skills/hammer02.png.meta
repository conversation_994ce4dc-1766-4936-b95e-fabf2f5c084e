fileFormatVersion: 2
guid: e314de9810d424a2fa5ef4f35550dbc4
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -8411781049077633367
    second: hammer02_0
  - first:
      213: 6963791868191523456
    second: hammer02_1
  - first:
      213: 2468038896555169320
    second: hammer02_2
  - first:
      213: 8379059150985471104
    second: hammer02_3
  - first:
      213: -6321222390206108189
    second: hammer02_4
  - first:
      213: 2409478200686243994
    second: hammer02_5
  - first:
      213: 4887580189032743194
    second: hammer02_6
  - first:
      213: 820199411100106285
    second: hammer02_7
  - first:
      213: -2263556671956910846
    second: hammer02_8
  - first:
      213: -6606594234031888156
    second: hammer02_9
  - first:
      213: -1908783051892496665
    second: hammer02_10
  - first:
      213: 7565846766116112981
    second: hammer02_11
  - first:
      213: 8071289961192113678
    second: hammer02_12
  - first:
      213: -4461495761330396809
    second: hammer02_13
  - first:
      213: -4458148403973681372
    second: hammer02_14
  - first:
      213: -3702411864719630584
    second: hammer02_15
  - first:
      213: 1543898251739060161
    second: hammer02_16
  - first:
      213: 7261192634354244004
    second: hammer02_17
  - first:
      213: -5528761732695722930
    second: hammer02_18
  - first:
      213: 7166025189255271
    second: hammer02_19
  - first:
      213: -9104700133630726228
    second: hammer02_20
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 1
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 1024
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: iOS
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: hammer02_0
      rect:
        serializedVersion: 2
        x: 2
        y: 0
        width: 364
        height: 1011
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ae6d5114b9534b80800000000000000
      internalID: -8411781049077633367
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_1
      rect:
        serializedVersion: 2
        x: 195
        y: 855
        width: 388
        height: 164
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0824f41fecb54a060800000000000000
      internalID: 6963791868191523456
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_2
      rect:
        serializedVersion: 2
        x: 347
        y: 600
        width: 48
        height: 264
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8269409dc4c304220800000000000000
      internalID: 2468038896555169320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_3
      rect:
        serializedVersion: 2
        x: 610
        y: 18
        width: 412
        height: 1001
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 084a97657e5684470800000000000000
      internalID: 8379059150985471104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_4
      rect:
        serializedVersion: 2
        x: 626
        y: 637
        width: 47
        height: 225
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ede4853ab18648a0800000000000000
      internalID: -6321222390206108189
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_5
      rect:
        serializedVersion: 2
        x: 645
        y: 859
        width: 17
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9073f558af207120800000000000000
      internalID: 2409478200686243994
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_6
      rect:
        serializedVersion: 2
        x: 346
        y: 759
        width: 6
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a118882594c24d340800000000000000
      internalID: 4887580189032743194
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_7
      rect:
        serializedVersion: 2
        x: 387
        y: 576
        width: 233
        height: 272
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2e3b9fc40fe16b00800000000000000
      internalID: 820199411100106285
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_8
      rect:
        serializedVersion: 2
        x: 350
        y: 632
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20dfa0f133b3690e0800000000000000
      internalID: -2263556671956910846
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_9
      rect:
        serializedVersion: 2
        x: 354
        y: 631
        width: 13
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e8a2612c89a054a0800000000000000
      internalID: -6606594234031888156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_10
      rect:
        serializedVersion: 2
        x: 395
        y: 475
        width: 211
        height: 168
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e669ae6de3a285e0800000000000000
      internalID: -1908783051892496665
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_11
      rect:
        serializedVersion: 2
        x: 406
        y: 616
        width: 8
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55a83675c894ff860800000000000000
      internalID: 7565846766116112981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_12
      rect:
        serializedVersion: 2
        x: 654
        y: 631
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e064808c47bf20070800000000000000
      internalID: 8071289961192113678
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_13
      rect:
        serializedVersion: 2
        x: 430
        y: 351
        width: 160
        height: 228
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 771adfa3e359512c0800000000000000
      internalID: -4461495761330396809
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_14
      rect:
        serializedVersion: 2
        x: 403
        y: 117
        width: 195
        height: 227
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42f8c2995a97122c0800000000000000
      internalID: -4458148403973681372
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_15
      rect:
        serializedVersion: 2
        x: 477
        y: 191
        width: 10
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 80fd13776046e9cc0800000000000000
      internalID: -3702411864719630584
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_16
      rect:
        serializedVersion: 2
        x: 486
        y: 191
        width: 9
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1c7d040b7470d6510800000000000000
      internalID: 1543898251739060161
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_17
      rect:
        serializedVersion: 2
        x: 495
        y: 191
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a91e50ca30f4c460800000000000000
      internalID: 7261192634354244004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_18
      rect:
        serializedVersion: 2
        x: 501
        y: 191
        width: 9
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e44f5ea2f74e543b0800000000000000
      internalID: -5528761732695722930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_19
      rect:
        serializedVersion: 2
        x: 512
        y: 191
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7686bd14675791000800000000000000
      internalID: 7166025189255271
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: hammer02_20
      rect:
        serializedVersion: 2
        x: 419
        y: 8
        width: 196
        height: 175
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: caf2d7c977b95a180800000000000000
      internalID: -9104700133630726228
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      hammer02_0: -8411781049077633367
      hammer02_1: 6963791868191523456
      hammer02_10: -1908783051892496665
      hammer02_11: 7565846766116112981
      hammer02_12: 8071289961192113678
      hammer02_13: -4461495761330396809
      hammer02_14: -4458148403973681372
      hammer02_15: -3702411864719630584
      hammer02_16: 1543898251739060161
      hammer02_17: 7261192634354244004
      hammer02_18: -5528761732695722930
      hammer02_19: 7166025189255271
      hammer02_2: 2468038896555169320
      hammer02_20: -9104700133630726228
      hammer02_3: 8379059150985471104
      hammer02_4: -6321222390206108189
      hammer02_5: 2409478200686243994
      hammer02_6: 4887580189032743194
      hammer02_7: 820199411100106285
      hammer02_8: -2263556671956910846
      hammer02_9: -6606594234031888156
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
