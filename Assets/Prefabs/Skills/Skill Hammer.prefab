%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5138558919472914074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7549832360961171734}
  - component: {fileID: 5221090870822893853}
  m_Layer: 0
  m_Name: HitBox
  m_TagString: Skill
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7549832360961171734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5138558919472914074}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.55, y: 0.55, z: 0.55}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 8745997737517559768}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &5221090870822893853
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5138558919472914074}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 2
  m_Sprite: {fileID: -2413806693520163455, guid: a86470a33a6bf42c4b3595704624658b, type: 3}
  m_Color: {r: 0, g: 1, b: 0.14509805, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &6151571635089827365
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7160721149542501867}
  - component: {fileID: 7334191546930318856}
  m_Layer: 0
  m_Name: hammer02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7160721149542501867
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6151571635089827365}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.396, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8745997737517559768}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &7334191546930318856
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6151571635089827365}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 3
  m_Sprite: {fileID: 21300000, guid: e314de9810d424a2fa5ef4f35550dbc4, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1001 &8684016687130089824
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.7248
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.7248
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.7248
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3344596882405017908, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_Icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 3344596882405017908, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_Name
      value: HammerSkill
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 2400624503451725491, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
    - {fileID: 3052163316000494566, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7160721149542501867}
    - targetCorrespondingSourceObject: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7549832360961171734}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3344596882405017908, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7204662685956568119}
  m_SourcePrefab: {fileID: 100100000, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
--- !u!1 &6262736928741085268 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3344596882405017908, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
  m_PrefabInstance: {fileID: 8684016687130089824}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7204662685956568119
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6262736928741085268}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3c916cbba21e54283ba8e72a7dc9967c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skillData: {fileID: 0}
  vector3Rotation: {x: 0, y: 0, z: -90}
  hitbox: {fileID: 5138558919472914074}
  secondsBeforeAnimating: 0.5
  secondsForRotating: 1
  ease: 28
--- !u!4 &8745997737517559768 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
  m_PrefabInstance: {fileID: 8684016687130089824}
  m_PrefabAsset: {fileID: 0}
