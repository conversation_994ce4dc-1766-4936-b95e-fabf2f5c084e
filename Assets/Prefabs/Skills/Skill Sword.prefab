%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &959915839612389684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6955163767900807909}
  m_Layer: 0
  m_Name: Pivot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6955163767900807909
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 959915839612389684}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4138678403291892792}
  m_Father: {fileID: 7363139255091051968}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3463669145966822971
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7419243020059526142}
  - component: {fileID: 8340141840734373378}
  m_Layer: 0
  m_Name: HitBox
  m_TagString: Skill
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7419243020059526142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3463669145966822971}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.75, y: 0.75, z: 0.75}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 7363139255091051968}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &8340141840734373378
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3463669145966822971}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 2
  m_Sprite: {fileID: -2413806693520163455, guid: a86470a33a6bf42c4b3595704624658b, type: 3}
  m_Color: {r: 0, g: 1, b: 0.14509805, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &8073420919054729384
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4138678403291892792}
  - component: {fileID: 2813024811835582752}
  m_Layer: 0
  m_Name: sword
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4138678403291892792
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8073420919054729384}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: 0.5349187, w: 0.8449035}
  m_LocalPosition: {x: 0, y: 0.922, z: 0}
  m_LocalScale: {x: 1.43, y: 1.43, z: 1.43}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 6955163767900807909}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 64.677}
--- !u!212 &2813024811835582752
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8073420919054729384}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 3
  m_Sprite: {fileID: 21300000, guid: 43a7bb429f3804d0ca4d46e30db4a54a, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1001 &7479623098493373304
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2400624503451725491, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 2400624503451725491, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_Color.b
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2400624503451725491, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_Color.g
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2400624503451725491, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_Color.r
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2400624503451725491, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_WasSpriteAssigned
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3344596882405017908, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      propertyPath: m_Name
      value: Skill Sword
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7419243020059526142}
    - targetCorrespondingSourceObject: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6955163767900807909}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3344596882405017908, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
      insertIndex: -1
      addedObject: {fileID: 723453798677108833}
  m_SourcePrefab: {fileID: 100100000, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
--- !u!1 &5307088299362370124 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3344596882405017908, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
  m_PrefabInstance: {fileID: 7479623098493373304}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &723453798677108833
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5307088299362370124}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bcb5ca96052b47549eac317460a49ec, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skillData: {fileID: 11400000, guid: 642177c2e7bac41268835f700114c542, type: 2}
  rotationValue: 180
  secondToRotate: 0.2
  hitboxGameObject: {fileID: 3463669145966822971}
  pivotTransform: {fileID: 6955163767900807909}
--- !u!4 &7363139255091051968 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 136187367201355448, guid: 74ddfd4deb7b5440caaec35a5d137850, type: 3}
  m_PrefabInstance: {fileID: 7479623098493373304}
  m_PrefabAsset: {fileID: 0}
