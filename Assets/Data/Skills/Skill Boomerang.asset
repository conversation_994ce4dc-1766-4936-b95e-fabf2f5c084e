%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b29f1008606d145348486e2867059bae, type: 3}
  m_Name: Skill Boomerang
  m_EditorClassIdentifier: 
  skillName: Boomerang
  description: Throws a boomerang that drags anything that crosses and gets back
  skillType: 1
  skillTypeLetter: 0
  amount: 6
  secondsToRemainPerCell: 0.2
  visualPrefab: {fileID: 6262736928741085268, guid: a81ef6ba36cad4570aee07e907094fd6, type: 3}
  chipSprite: {fileID: 21300000, guid: ee2255bd67bd940589cb2c1bb34b449a, type: 3}
  staysOnTile: 0
