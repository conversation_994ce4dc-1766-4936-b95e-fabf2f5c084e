%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b29f1008606d145348486e2867059bae, type: 3}
  m_Name: Skill Explode
  m_EditorClassIdentifier: 
  skillName: Explode
  description: Explodes 1 cell around the caster
  skillType: 1
  skillTypeLetter: 0
  amount: 10
  secondsToRemainPerCell: 1
  visualPrefab: {fileID: 6262736928741085268, guid: 93152dc0c0d4246a38367024d95a906c, type: 3}
  staysOnTile: 0
