%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b29f1008606d145348486e2867059bae, type: 3}
  m_Name: Skill Mouse
  m_EditorClassIdentifier: 
  skillName: Mouse
  description: Moves in zig zag to front, till the end of grid
  skillType: 1
  skillTypeLetter: 0
  amount: 10
  secondsToRemainPerCell: 0.2
  visualPrefab: {fileID: 131351572988332777, guid: 2c7c88db746ae4298b9064e4ae1d760b, type: 3}
  staysOnTile: 0
