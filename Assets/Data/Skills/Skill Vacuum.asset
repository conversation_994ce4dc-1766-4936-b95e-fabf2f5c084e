%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b29f1008606d145348486e2867059bae, type: 3}
  m_Name: Skill Vacuum
  m_EditorClassIdentifier: 
  skillName: Vacuum
  description: Drag an opponent towards him and then throws it to a random position
    in their side
  skillType: 1
  skillTypeLetter: 0
  amount: 10
  secondsToRemainPerCell: 0.3
  visualPrefab: {fileID: 6262736928741085268, guid: 87416ba654608497caadee0b4dfc4244, type: 3}
  staysOnTile: 0
