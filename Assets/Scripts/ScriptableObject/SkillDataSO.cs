using UnityEngine;

[CreateAssetMenu(menuName = "Skills/SkillData")]
public class SkillDataSO : ScriptableObject
{
    public string skillName = string.Empty;
    [TextArea]
    public string description = string.Empty;
    public ESkillType skillType = ESkillType.Attack;
    public ESkillTypeLetter skillTypeLetter = ESkillTypeLetter.A;
    /// <summary>
    /// Damage or Heal Amount
    /// </summary>
    [Range(0, 100), Tooltip("Can be damage or healing amount")]
    public int amount; // Daño, curación, etc.
    public float secondsToRemainPerCell = 1f;
    public GameObject visualPrefab;
    public Sprite chipSprite;
    public bool staysOnTile = false;
}