public class FiniteStateMachineController
{
    public EnemyState CurrentState { get; private set; }

    public void Initialize(EnemyState startingState)
    {
        CurrentState = startingState;
        if (CurrentState != null)
        {
            CurrentState.EnterState();
        }
    }

    public void ChangeState(EnemyState newState)
    {
        if (CurrentState != null)
        {
            CurrentState.ExitState();
        }

        CurrentState = newState;

        if (CurrentState != null)
        {
            CurrentState.EnterState();
        }
    }

    public void Update() => CurrentState?.UpdateState();
}