using System;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;

public class SelectedSkillsController : MonoBehaviour
{
    public GameObject rootGameObject;
    public SelectedSkillsItem itemTemplate1st;
    public SelectedSkillsItem itemTemplateOthers;
    public float offsetBetweenItems = 1f;
    public GridUnit gridUnitToSubscribe;

    [Header("Debug")]
    public SkillDataSO[] debug_skillDataSO;

    private Stack<SelectedSkillsItem> spawnedItems = new();
    private Dictionary<SkillDataSO, SelectedSkillsItem> skillDataSO2SelectedSkillsItem = new();

    public void Start()
    {
        if (itemTemplate1st != null)
        {
            itemTemplate1st.gameObject.SetActive(false);
        }
        if (itemTemplateOthers != null)
        {
            itemTemplateOthers.gameObject.SetActive(false);
        }
        if (gridUnitToSubscribe != null)
        {
            gridUnitToSubscribe.EventUseSkill += OnGridUnitUseSkill;
        }
    }

    public void Show(IEnumerable<SkillDataSO> skillDataSO)
    {
        if (skillDataSO == null)
        {
            Debug.LogError($"skillDataSO is NULL/EMPTY");
            return;
        }
        if (skillDataSO.Count() == 0)
        {
            return;
        }
        Clear();

        CreateClones(skillDataSO);
        OrderElements();
    }

    private void CreateClones(IEnumerable<SkillDataSO> skillDataSO)
    {
        foreach (var item in skillDataSO)
        {
            var elementToUse = itemTemplateOthers;
            var clone = Instantiate(elementToUse, rootGameObject.transform);
            clone.itemTemplate.sprite = item.chipSprite;
            clone.name = item.skillName;
            spawnedItems.Push(clone);
            clone.gameObject.SetActive(true);
        }
    }

    public void Clear()
    {
        foreach (var item in spawnedItems)
        {
            Destroy(item.gameObject);
        }
        spawnedItems.Clear();
    }

    public void RemoveFirst()
    {
        if (spawnedItems.Count == 0)
        {
            Debug.LogWarning($"No more spawnedItems");
            return;
        }
        var element = spawnedItems.Pop();
        OrderElements();
        Destroy(element.gameObject);
    }

    private void OrderElements()
    {
        var count = 0;
        foreach (var item in spawnedItems)
        {
            item.transform.localPosition = count * offsetBetweenItems * Vector3.right;
            count++;
        }
    }

    private void OnGridUnitUseSkill(GridUnit unit)
    {
        if (spawnedItems.Count == 0)
        {
            Debug.LogWarning($"No more spawnedItems");
            return;
        }
        RemoveFirst();
    }

    #region Unity
    [ContextMenu(nameof(Unity_Test))]
    public void Unity_Test()
    {
        Show(debug_skillDataSO);
    }
    [ContextMenu(nameof(Unity_RemoveFirst))]
    public void Unity_RemoveFirst()
    {
        RemoveFirst();
    }
    #endregion

}
