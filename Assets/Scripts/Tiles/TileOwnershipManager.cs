using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Tilemaps;

public class TileOwnershipManager : MonoBehaviour
{
    [SerializeField] public Tilemap tilemap;

    // Dictionary to store ownership data for positions where we don't have OwnershipTileSO
    private Dictionary<Vector3Int, OwnershipTileSO.Owner> ownershipData = new Dictionary<Vector3Int, OwnershipTileSO.Owner>();

    private void Awake()
    {
        if (tilemap == null)
        {
            // Debug.LogError("TileOwnershipManager requires a Tilemap component!");
        }
    }

    /// <summary>
    /// Set the owner of a tile at the specified position
    /// </summary>
    /// <param name="position">The grid position of the tile</param>
    /// <param name="owner">The new owner (Player, Enemy, or None)</param>
    public void SetTileOwner(Vector3Int position, OwnershipTileSO.Owner owner)
    {
        TileBase currentTile = tilemap.GetTile(position);

        if (currentTile is OwnershipTileSO OwnershipTileSO)
        {
            // If it's already an OwnershipTileSO, just update its owner
            OwnershipTileSO.TileOwner = owner;
            tilemap.RefreshTile(position); // Refresh the tile to update its appearance
        }
        else if (currentTile != null)
        {
            // If it's a different type of tile, store the ownership in our dictionary
            ownershipData[position] = owner;

            // Optionally, you could replace the tile with an OwnershipTileSO
            // This depends on your game design - whether you want to keep the original tile or replace it
        }
    }

    /// <summary>
    /// Get the owner of a tile at the specified position
    /// </summary>
    /// <param name="position">The grid position of the tile</param>
    /// <returns>The owner of the tile (Player, Enemy, or None)</returns>
    public OwnershipTileSO.Owner GetTileOwner(Vector3Int position)
    {
        TileBase currentTile = tilemap.GetTile(position);

        if (currentTile is OwnershipTileSO OwnershipTileSO)
        {
            // If it's an OwnershipTileSO, get the owner directly from it
            return OwnershipTileSO.TileOwner;
        }
        else if (ownershipData.TryGetValue(position, out OwnershipTileSO.Owner owner))
        {
            // If we have ownership data for this position, return it
            return owner;
        }

        // Default to None if no ownership information is available
        return OwnershipTileSO.Owner.None;
    }

    /// <summary>
    /// Replace a tile with an OwnershipTileSO and set its owner
    /// </summary>
    /// <param name="position">The grid position of the tile</param>
    /// <param name="OwnershipTileSO">The OwnershipTileSO to use</param>
    /// <param name="owner">The owner to set</param>
    public void ReplaceTileWithOwnershipTileSO(Vector3Int position, OwnershipTileSO OwnershipTileSO, OwnershipTileSO.Owner owner)
    {
        if (OwnershipTileSO != null)
        {
            // Set the owner
            OwnershipTileSO.TileOwner = owner;

            // Replace the tile
            tilemap.SetTile(position, OwnershipTileSO);

            // Remove from our dictionary if it was there
            ownershipData.Remove(position);
        }
    }

    /// <summary>
    /// Get all tiles owned by a specific owner
    /// </summary>
    /// <param name="owner">The owner to search for</param>
    /// <returns>A list of positions where tiles are owned by the specified owner</returns>
    public List<Vector3Int> GetTilesByOwner(OwnershipTileSO.Owner owner)
    {
        List<Vector3Int> positions = new List<Vector3Int>();

        // Check all tiles in the tilemap bounds
        BoundsInt bounds = tilemap.cellBounds;
        foreach (Vector3Int pos in bounds.allPositionsWithin)
        {
            if (GetTileOwner(pos) == owner)
            {
                positions.Add(pos);
            }
        }

        return positions;
    }
}
