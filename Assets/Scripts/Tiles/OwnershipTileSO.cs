using UnityEngine;
using UnityEngine.Tilemaps;

[CreateAssetMenu(fileName = "New Ownership Tile", menuName = "Tiles/Ownership Tile")]
public partial class OwnershipTileSO : Tile
{

    [SerializeField]
    private Owner tileOwner = Owner.None;

    // Color tints for different owners
    [SerializeField]
    private Color playerColor = new Color(0.2f, 0.7f, 1f, 1f);  // Blue for player
    
    [SerializeField]
    private Color enemyColor = new Color(1f, 0.3f, 0.3f, 1f);   // Red for enemy
    
    [SerializeField]
    private Color neutralColor = new Color(1f, 1f, 1f, 1f);     // White for neutral

    // Property to get/set the owner
    public Owner TileOwner
    {
        get { return tileOwner; }
        set { tileOwner = value; }
    }

    // Override the GetTileData method to apply the color based on ownership
    public override void GetTileData(Vector3Int position, ITilemap tilemap, ref TileData tileData)
    {
        base.GetTileData(position, tilemap, ref tileData);

        // Apply color based on ownership
        switch (tileOwner)
        {
            case Owner.Player:
                tileData.color = playerColor;
                break;
            case Owner.Enemy:
                tileData.color = enemyColor;
                break;
            case Owner.None:
            default:
                tileData.color = neutralColor;
                break;
        }
    }
}
