using UnityEngine;
using UnityEngine.Tilemaps;

public class ShowCoordinates : MonoBehaviour
{
    public Tilemap tilemap;
    public Camera mainCamera;

    // Reference to the TileOwnershipManager
    private TileOwnershipManager ownershipManager;

    void Start()
    {
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }

        // Get or add the TileOwnershipManager component
        ownershipManager = GetComponent<TileOwnershipManager>();
        if (ownershipManager == null)
        {
            ownershipManager = gameObject.AddComponent<TileOwnershipManager>();
            ownershipManager.tilemap = tilemap;
        }
    }

    void Update()
    {
        Vector3 mousePosition = Input.mousePosition;

        // Aseguramos que la coordenada Z sea correcta para la conversión
        mousePosition.z = -mainCamera.transform.position.z;

        // Convertimos directamente de Screen a World con la Z correcta
        Vector3 worldPosition = mainCamera.ScreenToWorldPoint(mousePosition);

        // Para tilemaps hexagonales, es importante que la posición Z sea 0
        worldPosition.z = 0;

        // Convertimos a coordenadas de celda
        Vector3Int cellPosition = tilemap.WorldToCell(worldPosition);

        // Verificamos si la celda existe en el tilemap
        bool hasTile = tilemap.HasTile(cellPosition);

        // Display current cell info
        if (hasTile)
        {
            OwnershipTileSO.Owner owner = ownershipManager.GetTileOwner(cellPosition);
            string ownerStr = owner.ToString();
            Debug.Log($"Cell: {cellPosition}, Owner: {ownerStr}");
        }

        // Dibujamos un gizmo en tiempo de ejecución para ver dónde está el punto
        Debug.DrawRay(worldPosition, Vector3.up * 0.5f, Color.red);
    }

    // Método para visualizar mejor la celda seleccionada
    void OnDrawGizmos()
    {
        if (tilemap != null && Application.isPlaying)
        {
            Vector3 mousePosition = Input.mousePosition;
            mousePosition.z = -Camera.main.transform.position.z;
            Vector3 worldPosition = Camera.main.ScreenToWorldPoint(mousePosition);
            worldPosition.z = 0;
            Vector3Int cellPosition = tilemap.WorldToCell(worldPosition);

            // Dibujamos un cubo en la posición de la celda
            Vector3 cellCenter = tilemap.GetCellCenterWorld(cellPosition);

            // Change gizmo color based on ownership if available
            if (ownershipManager != null && tilemap.HasTile(cellPosition))
            {
                OwnershipTileSO.Owner owner = ownershipManager.GetTileOwner(cellPosition);
                Gizmos.color = owner switch
                {
                    OwnershipTileSO.Owner.Player => Color.blue,
                    OwnershipTileSO.Owner.Enemy => Color.red,
                    _ => Color.yellow
                };
            }
            else
            {
                Gizmos.color = Color.yellow;
            }

            Gizmos.DrawWireCube(cellCenter, Vector3.one * 0.5f);
        }
    }
}
