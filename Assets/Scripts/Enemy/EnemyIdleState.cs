public class EnemyIdleState : EnemyState
{
    public EnemyIdleState(FiniteStateMachineController stateMachine, Enemy enemy) : base(stateMachine, enemy)
    {
    }

    public override void EnterState()
    {
        // Debug.Log("Enemy entering Idle State");
        // Initialize idle behavior, e.g., stop moving, play idle animation
    }

    public override void UpdateState()
    {
        // Logic for idle state
        
    }

    public override void ExitState()
    {
        // Debug.Log("Enemy exiting Idle State");
    }
}
