using System;
using UnityEngine;

public class EnemyWaitSecondsState : EnemyState
{
    private float secondsToWait;
    private readonly EnemyState nextState;

    public EnemyWaitSecondsState(FiniteStateMachineController stateMachine, Enemy enemy, float secondsToWait, EnemyState nextState) : base(stateMachine, enemy)
    {
        this.secondsToWait = Math.Clamp(secondsToWait, 0, 10f);
        this.nextState = nextState;
    }

    public override void UpdateState()
    {
        if (secondsToWait > 0)
        {
            secondsToWait -= Time.deltaTime;
            return;
        }

        stateMachine.ChangeState(nextState);
    }

}
