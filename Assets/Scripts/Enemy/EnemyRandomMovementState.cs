using UnityEngine;
public class EnemyRandomMovementState : EnemyState
{
    private const float kTotalMovementsBeforeContinuing = 4f;

    private int amountOfMovements;
    private CombatController combatController;

    public EnemyRandomMovementState(FiniteStateMachineController stateMachine, Enemy enemy, CombatController combatController) : base(stateMachine, enemy)
    {
        this.combatController = combatController;
    }

    public override void EnterState()
    {
        amountOfMovements = 0;
        // Debug.Log("Enemy entering ChaseVertical State");
    }

    public override void UpdateState()
    {
        if (enemy.isMoving || !enemy.CanMove)
        {
            // Debug.LogError($"isMoving");
            return; // Esperar si se está moviendo o si falta alguna referencia crítica
        }

        if (amountOfMovements >= kTotalMovementsBeforeContinuing)
        {
            stateMachine.ChangeState(new EnemyPrepareAttackState(stateMachine, enemy));
            return;
        }
        else
        {
            Vector3Int nextCell = GetRandomEnemyGridPosition();
            Debug.Log($"Trying to move to {nextCell}");
            enemy.TryMoveToCell(nextCell);
            amountOfMovements++;
        }
    }

    private Vector3Int GetRandomEnemyGridPosition()
    {
        return new Vector3Int(
                        Random.Range(combatController.enemyTopLeft.x, combatController.enemyTopLeft.x + 4),
                        Random.Range(combatController.enemyTopLeft.y, combatController.enemyBottomRight.y),
                        0
                    );
    }

    public override void ExitState()
    {
        // Debug.Log("Enemy exiting ChaseVertical State");
    }
}
