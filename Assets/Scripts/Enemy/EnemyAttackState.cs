using UnityEngine;

public class EnemyAttackState : EnemyState
{
    public EnemyAttackState(FiniteStateMachineController stateMachine, Enemy enemy) : base(stateMachine, enemy)
    {
    }

    public override void EnterState()
    {
        enemy.UseSkill();

        // Después del ataque, volver a perseguir (o a un estado Idle/Patrol)
        // stateMachine.ChangeState(new EnemyIdleState(stateMachine, enemy));
    }

    public override void UpdateState()
    {
        // Para un ataque simple que ocurre instantáneamente en EnterState,
        // UpdateState puede estar vacío.
        // Si el ataque tuviera una duración, se manejaría aquí.
    }

    public override void ExitState()
    {
        // Debug.Log("Enemy exiting Attack State");
    }
}
