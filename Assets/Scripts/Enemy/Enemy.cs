using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Tilemaps;

public class Enemy : GridUnit
{
    [Header("Enemy Settings")]
    public EnemyDataSO enemyData;
    public TMPro.TMP_Text healthText;

    #region Properties
    public FiniteStateMachineController StateMachine { get; private set; }
    public Tilemap Tilemap => tilemap;
    public float MoveSpeed => enemyData != null ? enemyData.moveSpeed : 1.0f;
    public float AttackPrepareDuration => enemyData != null ? enemyData.attackPrepareDuration : 1.0f;
    public string EnemyName => enemyData != null ? enemyData.enemyName : "Enemy";
    #endregion

    override protected void Awake()
    {
        base.Awake();
        StateMachine = new FiniteStateMachineController();

        if (enemyData != null)
        {
            currentHealthPoint = enemyData.maxHealth;
            maximumHealthPoint = currentHealthPoint;
            moveSpeed = enemyData.moveSpeed;
            if (availableSkills.Count == 0)
            {
                for (int i = enemyData.availableSkills.Length - 1; i >= 0; i--)
                {
                    availableSkills.Add(enemyData.availableSkills[i]);
                }
            }
        }
        var currentGridPosition = tilemap.WorldToCell(transform.position);
        SnapToCell(currentGridPosition);
        combatController.AddGridUnit(currentGridPosition, this);
        EventTakingDamage += OnTakingDamage;
        UpdateHealth();
        // Debug.Log($"{name} {currentGridPosition}");
    }

    override protected void Start()
    {
        base.Start();
        var foes = GetGridUnitFoes();
        if (foes.Count == 0)
        {
            Debug.LogWarning("Enemy: No hay enemigos para perseguir. El enemigo permanecerá inactivo.", this);
            return;
        }

        combatController.UpdateGridUnit(this, currentGridPosition);
        // Debug.Log($"Enemy - Start - StateMachine.Initialize(new EnemyChaseVerticalState");
        StateMachine.Initialize(new EnemyChaseVerticalState(StateMachine, this, foes[0]));
        // StateMachine.Initialize(new EnemyRandomMovementState(StateMachine, this, combatController));
        // StateMachine.Initialize(new EnemyAttackState(StateMachine, this));
    }

    private void OnTakingDamage(GridUnit unit) => UpdateHealth();

    private void UpdateHealth()
    {
        if (healthText == null)
        {
            return;
        }
        healthText.text = currentHealthPoint.ToString();
    }

    override protected void Update()
    {
        base.Update();
        StateMachine.Update();
    }

    public List<GridUnit> GetGridUnitFoes() => combatController.GetGridUnitFoes(tag);

    override protected void OnSkillFinish(SkillBehaviour skillBehaviour)
    {
        base.OnSkillFinish(skillBehaviour);
        // Debug.Log("OnSkillFinish");
        Invoke(nameof(ChangeToChase), 1f);
    }

    private void ChangeToChase()
    {
        // Aquí puedes agregar lógica adicional si lo necesitas
        var foes = GetGridUnitFoes();
        if (foes.Count == 0)
        {
            Debug.LogWarning("Enemy: No hay enemigos para perseguir. El enemigo permanecerá inactivo.", this);
            return;
        }
        // Debug.Log($"{name} OnSkillFinish going to EnemyChaseVerticalState");
        StateMachine.ChangeState(new EnemyChaseVerticalState(StateMachine, this, foes[0]));
    }

    public void Heal(int amount)
    {
        currentHealthPoint += amount;
        if (enemyData != null && currentHealthPoint > enemyData.maxHealth)
        {
            currentHealthPoint = enemyData.maxHealth;
        }
        // Puedes agregar efectos visuales aquí
    }

    protected override void Die()
    {
        base.Die();
        EventTakingDamage -= OnTakingDamage;
        Destroy(gameObject);
    }

    #region Unity
    [ContextMenu(nameof(Unity_UseSkill))]
    public void Unity_UseSkill()
    {
        StateMachine.ChangeState(new EnemyAttackState(StateMachine, this));
    }
    #endregion

}
