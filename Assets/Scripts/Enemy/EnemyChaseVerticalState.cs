using UnityEngine;
public class EnemyChaseVerticalState : EnemyState
{
    private GridUnit gridUnitToFocus;

    public EnemyChaseVerticalState(FiniteStateMachineController stateMachine, Enemy enemy, GridUnit gridUnitToFocus) : base(stateMachine, enemy)
    {
        this.gridUnitToFocus = gridUnitToFocus;
    }

    public override void EnterState()
    {
        // Debug.Log("Enemy entering ChaseVertical State");
    }

    public override void UpdateState()
    {
        if (enemy.isMoving || !enemy.CanMove)
        {
            // Debug.LogError($"isMoving");
            return; // Esperar si se está moviendo o si falta alguna referencia crítica
        }

        int playerTargetY = gridUnitToFocus.currentGridPosition.y;
        int enemyCurrentY = enemy.currentGridPosition.y;

        // Debug.Log($"enemy {enemy.name} is at {enemy.currentGridPosition} and player is at {gridUnitToFocus.currentGridPosition}.");
        if (enemyCurrentY == playerTargetY)
        {
            // Debug.Log($"Changing EnemyPrepareAttackState");
            // Misma fila, prepararse para atacar
            stateMachine.ChangeState(new EnemyPrepareAttackState(stateMachine, enemy));
        }
        else
        {
            // Moverse verticalmente hacia el jugador
            int directionY = (playerTargetY > enemyCurrentY) ? 1 : -1;
            var nextCell = enemy.currentGridPosition + new Vector3Int(0, directionY, 0);
            // Debug.Log($"Trying to move to {nextCell}");
            enemy.TryMoveToCell(nextCell);
        }
    }

    public override void ExitState()
    {
        // Debug.Log("Enemy exiting ChaseVertical State");
    }
}
