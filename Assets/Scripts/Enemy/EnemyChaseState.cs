using UnityEngine;

public class EnemyChaseState : EnemyState
{
    public EnemyChaseState(FiniteStateMachineController stateMachine, Enemy enemy) : base(stateMachine, enemy)
    {
    }

    public override void EnterState()
    {
        Debug.Log("Enemy entering Chase State");
        // Initialize chase behavior, e.g., start moving towards player, play run animation
    }

    public override void UpdateState()
    {
        if (!enemy.CanMove)
        {
            return;
        }
    }

    public override void ExitState()
    {
        Debug.Log("Enemy exiting Chase State");
        // Clean up chase behavior, e.g., stop movement effects
    }
}
