using System.Collections.Generic;
using UnityEngine;

public class EnemyPrepareAttackState : EnemyState
{
    private float _waitTimer;
    private int _playerRowAtStartOfWait;

    private GridUnit gridUnitToFocus;

    public EnemyPrepareAttackState(FiniteStateMachineController stateMachine, Enemy enemy) : base(stateMachine, enemy)
    {
        var foes = enemy.GetGridUnitFoes();
        gridUnitToFocus = foes[0];
    }

    public override void EnterState()
    {
        // Debug.Log("Enemy entering PrepareAttack State");
        _waitTimer = enemy.enemyData.attackPrepareDuration;
        if (gridUnitToFocus != null && enemy.Tilemap != null)
        {
            _playerRowAtStartOfWait = gridUnitToFocus.currentGridPosition.y;
        }
        else
        {
            // Si falta el jugador o el tilemap, no se puede preparar el ataque, volver a un estado seguro. 
            // Esto podría ser un estado Idle o reintentar Chase. Por ahora, Chase.
            Debug.LogWarning("PrepareAttackState: Player or Tilemap missing, returning to <PERSON>.");
            stateMachine.ChangeState(new EnemyChaseVerticalState(stateMachine, enemy, gridUnitToFocus));
        }
    }

    public override void UpdateState()
    {
        if (!enemy.CanAction)
        {
            return;
        }
        if (gridUnitToFocus == null)
            {
                // Debug.Log($"Back to EnemyChaseVerticalState");
                stateMachine.ChangeState(new EnemyChaseVerticalState(stateMachine, enemy, gridUnitToFocus));
                return;
            }

        _waitTimer -= Time.deltaTime;

        if (gridUnitToFocus.currentGridPosition.y != _playerRowAtStartOfWait)
        {
            // El jugador se movió de fila, volver a perseguir
            // Debug.Log("Player moved rows, back to chasing.");
            stateMachine.ChangeState(new EnemyChaseVerticalState(stateMachine, enemy, gridUnitToFocus));
            return;
        }

        if (_waitTimer > 0)
        {
            // Debug.Log($"_waitTimer > 0 ({_waitTimer})");
            return;
        }

        // Tiempo de espera terminado y el jugador sigue en la misma fila
        // Debug.Log("Preparation complete, attacking.");
        stateMachine.ChangeState(new EnemyAttackState(stateMachine, enemy));
    }

}
