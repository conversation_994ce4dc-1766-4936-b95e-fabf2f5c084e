using UnityEngine;

public abstract class EnemyState
{
    protected FiniteStateMachineController stateMachine;
    protected Enemy enemy;

    public EnemyState(FiniteStateMachineController stateMachine, Enemy enemy)
    {
        this.stateMachine = stateMachine;
        this.enemy = enemy;
    }

    public virtual void EnterState() { }
    public virtual void ExitState() { }
    public virtual void UpdateState() { }
}