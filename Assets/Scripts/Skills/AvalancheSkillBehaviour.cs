using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

/// <summary>
/// Grant inmunity to the caster and moves it one tile horizontally per second till hitting something or reaching end of grid. Stop at the end of the grid and return to beginning position.
/// </summary>
public class AvalancheSkillBehaviour : SkillBehaviour
{
    private Vector3 nextPosition;
    private Vector3Int nextGridPosition;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition;

        nextGridPosition = currentGridPosition + base.directionToUse;
        nextPosition = tilemap.GetCellCenterWorld(nextGridPosition);
        combatController.UpdateGridUnit(caster, currentGridPosition);
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        SetTweenToNextGridPosition();
    }

    private void SetTweenToNextGridPosition()
    {
        caster.transform.DOMove(nextPosition, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(gameObject)
            .onComplete += OnArriveNextGridPosition;
    }

    private void OnArriveNextGridPosition()
    {
        currentGridPosition = nextGridPosition;
        if (combatController.TryGetGridUnitFromPosition(currentGridPosition, out var gridUnit))
        {
            if (!caster.CompareTag(gridUnit.tag))
            {
                // If opponent is different, damage
                DoDamageInCurrentGridPosition();
            }
        }
        combatController.UpdateSkill(this, currentGridPosition);
        combatController.UpdateGridUnit(caster, currentGridPosition);
        nextGridPosition = currentGridPosition + base.directionToUse;
        nextPosition = tilemap.GetCellCenterWorld(nextGridPosition);
        // Is next grid position valid?
        if (!tilemap.HasTile(nextGridPosition))
        {
            // Debug.Log($"No tile in nextGridPosition {nextGridPosition}");
            // Reach the end of the grid
            InvokeFinishAndDestroy();
            return;
        }
        SetTweenToNextGridPosition();
    }

    override protected void InvokeFinishAndDestroy()
    {
        caster.transform.position = tilemap.GetCellCenterWorld(startGridPosition);
        if (combatController.TryGetGridUnitFromPosition(startGridPosition, out var gridUnit))
        {
            if (gridUnit == caster)
            {
                combatController.UpdateGridUnit(caster, startGridPosition);
                caster.TryMoveToCell(startGridPosition);
            }
            else
            {
                Debug.Log($"Position {startGridPosition} is used by someone else {gridUnit.name}");
            }
        }
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        base.InvokeFinishAndDestroy();
    }
}
