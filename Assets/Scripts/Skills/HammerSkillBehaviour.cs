using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class HammerSkillBehaviour : SkillBehaviour
{
    public Vector3 vector3Rotation;
    public GameObject hitbox;
    public float secondsBeforeAnimating = 0.5f;
    public float secondsForRotating = 1f;
    public Ease ease;

    private Vector3Int hitboxGridPosition;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition;
        hitboxGridPosition = currentGridPosition + directionToUse;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);
        vector3Rotation.z *= directionToUse.x;
        hitbox.transform.position = tilemap.GetCellCenterWorld(currentGridPosition + directionToUse);
        hitbox.transform.parent = null;
        transform.DORotate(vector3Rotation, secondsForRotating, RotateMode.Fast)
            .SetEase(ease).SetDelay(secondsBeforeAnimating).onComplete += InvokeFinishAndDestroy;
    }

    override protected void InvokeFinishAndDestroy()
    {
        currentGridPosition = hitboxGridPosition;
        DoDamageInCurrentGridPosition();
        hitbox.transform.parent = transform;
        base.InvokeFinishAndDestroy();
    }

}
