using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class SwordSkillBehaviour : SkillBehaviour
{
    public float rotationValue;
    public float secondToRotate;
    public GameObject hitboxGameObject;
    public Transform pivotTransform;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition + directionToUse;
        combatController.UpdateSkill(this, currentGridPosition);
        combatController.EventGridUnitEnter += OnGridUnitEnter;

        if (hitboxGameObject != null)
        {
            hitboxGameObject.transform.position = tilemap.GetCellCenterWorld(currentGridPosition);
        }
        else
        {
            Debug.LogError($"hitboxGameObject is NULL/EMPTY");
        }

        caster.BlockMovement(secondToRotate);
        TweenToDestination();
        DoDamageInCurrentGridPosition();
        Invoke(nameof(InvokeFinishAndDestroy), skillData.secondsToRemainPerCell);
    }

    private void OnGridUnitEnter(GridUnit unit, Vector3Int gridPosition)
    {
        if (unit.CompareTag(caster.tag))
        {
            return;
        }
        DoDamageInCurrentGridPosition();
    }

    private void TweenToDestination()
    {
        if (pivotTransform != null)
        {
            pivotTransform.DORotate(Vector3.back * rotationValue * directionToUse.x, secondToRotate).SetEase(Ease.Linear).SetLink(gameObject);
        }
        else
        {
            Debug.LogError($"pivotTransform is NULL/EMPTY");
        }
    }

    override protected void InvokeFinishAndDestroy()
    {
        this.DOKill();
        CancelInvoke();
        combatController.EventGridUnitEnter -= OnGridUnitEnter;
        base.InvokeFinishAndDestroy();
    }

}
