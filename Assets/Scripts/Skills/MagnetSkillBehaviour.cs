using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class MagnetSkillBehaviour : SkillBehaviour
{
    /// <summary>
    /// At the end of the skill, movement will be blocked
    /// </summary>
    [Tooltip("At the end of the skill, movement and action will be unblocked in x seconds")]
    public float secondsToUnblockMovement;

    [SerializeField] private SpriteRenderer thisSpriteRenderer;

    private Vector3 nextPosition;
    private Vector3Int nextGridPosition;
    private GridUnit grabbedUnit;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        if (thisSpriteRenderer != null)
        {
            if (caster.CompareTag("Enemy"))
            {
                thisSpriteRenderer.flipX = true;
            }
        }
        currentGridPosition = startGridPosition + directionToUse;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);

        Invoke(nameof(DetectEnemyInRow), 0.5f);
    }

    private void DetectEnemyInRow()
    {
        if (tilemap == null)
        {
            Debug.LogError($"tilemap is NULL/EMPTY");
            InvokeFinishAndDestroy();
            return;
        }
        var foes = combatController.GetGridUnitFoes(caster.tag);
        grabbedUnit = null;
        var distanceToClosest = 100f;
        Vector3Int gridPositionClosesOpponent = new();
        foreach (var item in foes)
        {
            if (item.currentGridPosition.y != caster.currentGridPosition.y)
            {
                continue;
            }
            if (grabbedUnit != null)
            {
                var distance = Vector3Int.Distance(grabbedUnit.currentGridPosition, item.currentGridPosition);
                if (distance < distanceToClosest)
                {
                    grabbedUnit = item;
                    distanceToClosest = distance;
                    gridPositionClosesOpponent = item.currentGridPosition;
                }
            }
            else
            {
                grabbedUnit = item;
                gridPositionClosesOpponent = grabbedUnit.currentGridPosition;
            }
        }
        if (grabbedUnit != null)
        {
            DragClosestOpponent(grabbedUnit, gridPositionClosesOpponent);
        }
        else
        {
            Debug.Log($"{name} Didn't find anyone to grab");
            InvokeFinishAndDestroy();
        }
    }

    private void DragClosestOpponent(GridUnit closestOpponent, Vector3Int gridPositionClosesOpponent)
    {
        if (closestOpponent == null)
        {
            InvokeFinishAndDestroy();
            return;
        }
        currentGridPosition = gridPositionClosesOpponent;
        combatController.UpdateSkill(this, currentGridPosition);
        DoDamageInCurrentGridPosition();
        if (grabbedUnit.currentHealthPoint == 0)
        {
            InvokeFinishAndDestroy();
            return;
        }
        grabbedUnit.BlockMovementAndAction(10f);

        nextGridPosition = currentGridPosition;
        OnArriveNextGridPosition();
    }

    private void SetTweenToNextGridPosition()
    {
        grabbedUnit.transform.DOMove(nextPosition, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(gameObject)
            .onComplete += OnArriveNextGridPosition; ;
    }

    private void OnArriveNextGridPosition()
    {
        currentGridPosition = nextGridPosition;

        combatController.UpdateSkill(this, currentGridPosition);
        if (grabbedUnit != null)
        {
            combatController.UpdateGridUnit(grabbedUnit, currentGridPosition);
            grabbedUnit.SnapToCell(currentGridPosition);
        }

        nextGridPosition = currentGridPosition - directionToUse;
        nextPosition = tilemap.GetCellCenterWorld(nextGridPosition);

        if (nextGridPosition == startGridPosition)
        {
            InvokeFinishAndDestroy();
            return;
        }

        var nextTile = tilemap.GetTile<OwnershipTileSO>(nextGridPosition);
        if (nextTile != null && caster.CompareTag(nextTile.TileOwner.ToString()))
        {
            // Next tile is opposite, stop the skill here
            InvokeFinishAndDestroy();
            return;
        }

        // Is next grid position valid?
        if (!tilemap.HasTile(nextGridPosition))
        {
            // Debug.Log($"No tile in nextGridPosition {nextGridPosition}");
            // Reach the end of the grid
            InvokeFinishAndDestroy();
            return;
        }
        SetTweenToNextGridPosition();
    }

    override protected void InvokeFinishAndDestroy()
    {
        CancelInvoke();
        if (grabbedUnit != null)
        {
            grabbedUnit.BlockMovementAndAction(secondsToUnblockMovement);
        }
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        base.InvokeFinishAndDestroy();
    }
}
