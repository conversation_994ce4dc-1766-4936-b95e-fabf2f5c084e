using System;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class ClockSkillBehaviour : SkillBehaviour
{
    [Min(1)]
    public int amountOfPositionToExplode = 5;
    public float secondsToExplode = 2.5f;
    public GameObject hitbox;

    public List<Vector3Int> gridPositionsToExplode = new();
    public List<GameObject> spawnedHitboxes = new();

    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);

        var emergencyExit = 50;

        AddPlayerPosition();
        if (gridPositionsToExplode.Count == 0)
        {
            Debug.LogError($"No enemy detected. Abort skill");
            InvokeFinishAndDestroy();
            return;
        }

        Vector3Int randomGridPosition;
        while (gridPositionsToExplode.Count < amountOfPositionToExplode)
        {
            if (emergencyExit-- <= 0)
            {
                break;
            }
            randomGridPosition = combatController.Helper.GetRandomPlayerPositionInGrid();
            if (gridPositionsToExplode.Contains(randomGridPosition))
            {
                continue;
            }
            gridPositionsToExplode.Add(randomGridPosition);
            SpawnHitbox(randomGridPosition);
        }
        if (emergencyExit <= 0)
        {
            Debug.LogError($"Emergency exit couldn't find {amountOfPositionToExplode} position to explode");
            InvokeFinishAndDestroy();
            return;
        }

        Invoke(nameof(Explode), secondsToExplode);
    }

    private void SpawnHitbox(Vector3Int randomGridPosition)
    {
        if (hitbox == null)
        {
            Debug.LogError($"hitbox is NULL/EMPTY");
            return;
        }
        var positionToUse = tilemap.GetCellCenterWorld(randomGridPosition);
        spawnedHitboxes.Add(Instantiate(hitbox, positionToUse, hitbox.transform.rotation, transform));
    }

    private void AddPlayerPosition()
    {
        var foes = combatController.GetGridUnitFoes(caster.tag);
        if (foes.Count == 0)
        {
            return;
        }
        var playerGridPosition = foes[0].currentGridPosition;
        gridPositionsToExplode.Add(playerGridPosition);
        SpawnHitbox(playerGridPosition);
    }

    private void Explode()
    {
        foreach (var item in gridPositionsToExplode)
        {
            currentGridPosition = item;
            DoDamageInCurrentGridPosition();
        }

        Invoke(nameof(InvokeFinishAndDestroy), 1f);
    }
}
