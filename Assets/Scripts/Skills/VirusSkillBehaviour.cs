using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class VirusSkillBehaviour : SkillBehaviour
{
    public float jumpValue = 1.5f;
    public int jumpsCount = 1;
    public GameObject hitbox;
    public GameObject bombGameObject;
    public float secondsBetweenThrows = 1.5f;

    [SerializeField] private Dictionary<Vector3Int, GameObject> gridPosition2MocusSticked = new();
    [SerializeField] private Dictionary<Vector3Int, GameObject> gridPosition2TravelingMocus = new();
    [SerializeField] private HashSet<Vector3Int> mocusGridPositionToRemoveSet = new();
    [SerializeField] private float secondsToBlockMovement = 2f;
    [SerializeField] private Vector3Int playerGridPosition;

    private const int totalMocusToSpawn = 2;

    private int mocusLanded = 0;
    private HashSet<Sequence> movingTweenMocusSet = new();

    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        combatController.RemoveSkill(this);
        hitbox.SetActive(false);
        bombGameObject.SetActive(false);

        Invoke(nameof(ThrowMocusToPlayer), 1f);
        Invoke(nameof(ThrowMocusToRandom), secondsBetweenThrows);
    }

    private void ThrowMocusToPlayer()
    {
        var foes = combatController.GetGridUnitFoes(caster.tag);
        if (foes.Count == 0)
        {
            Debug.LogError($"Player not found");
            InvokeFinishAndDestroy();
            return;
        }
        playerGridPosition = foes[0].currentGridPosition;
        ThrowMocus(playerGridPosition);
        if (gridPosition2TravelingMocus.Count == 0)
        {
            Debug.LogError($"No mocus were added to traveling");
            InvokeFinishAndDestroy();
            return;
        }
    }

    private void ThrowMocusToRandom()
    {
        Vector3Int randomGridPosition = playerGridPosition;
        var emergencyExit = 30;
        while (
            gridPosition2MocusSticked.ContainsKey(randomGridPosition) ||
            gridPosition2TravelingMocus.ContainsKey(randomGridPosition)
            )
        {
            if (emergencyExit-- <= 0)
            {
                break;
            }
            randomGridPosition = combatController.Helper.GetRandomPlayerPositionInGrid();
        }
        if (emergencyExit <= 0)
        {
            Debug.LogError($"emergencyExit");
            return;
        }
        ThrowMocus(randomGridPosition);
    }

    private void ThrowMocus(Vector3Int gridPositionToLand)
    {
        var positionToLand = tilemap.GetCellCenterWorld(gridPositionToLand);
        var hitboxGameObject = GetNewHitbox(positionToLand);
        var bombGameObject = GetNewBomb(positionToLand);
        if (hitboxGameObject == null)
        {
            Debug.LogError($"hitboxGameObject is NULL/EMPTY");
            return;
        }
        if (bombGameObject == null)
        {
            Debug.LogError($"bombGameObject is NULL/EMPTY");
            return;
        }

        hitboxGameObject.transform.position = positionToLand;
        bombGameObject.transform.position = tilemap.GetCellCenterWorld(currentGridPosition);

        hitboxGameObject.SetActive(true);
        bombGameObject.SetActive(true);

        Sequence tween = bombGameObject.transform.DOJump(positionToLand, jumpValue, jumpsCount, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(caster.gameObject)
        ;
        tween.onComplete += () => OnLand(hitboxGameObject, bombGameObject, gridPositionToLand, tween);
        movingTweenMocusSet.Add(tween);
        gridPosition2TravelingMocus[gridPositionToLand] = bombGameObject;
    }

    private void OnLand(GameObject hitboxGameObject, GameObject bombGameObject, Vector3Int gridPositionToLand, Sequence tween)
    {
        movingTweenMocusSet.Remove(tween);
        hitboxGameObject.SetActive(false);
        gridPosition2MocusSticked.Add(gridPositionToLand, bombGameObject);
        if (gridPosition2MocusSticked.Count == 1)
        {
            // combatController.AddSkill(gridPositionToLand, this);
            combatController.EventGridUnitEnter += OnGridUnitEnter;
        }
        gridPosition2TravelingMocus.Remove(gridPositionToLand);
        DoEffect(gridPositionToLand);
        mocusLanded++;
        if (mocusLanded >= totalMocusToSpawn)
        {
            // Debug.Log($"mocus landed");
            InvokeFinish();
        }
    }

    private void OnGridUnitEnter(GridUnit unit, Vector3Int gridPosition)
    {
        if (gridPosition2MocusSticked.Count == 0)
        {
            return;
        }
        if (!gridPosition2MocusSticked.ContainsKey(gridPosition))
        {
            return;
        }
        if (unit.CompareTag(caster.tag))
        {
            // Ignoring allies
            return;
        }
        DoEffect(gridPosition, unit);
    }

    private void RemoveMocus()
    {

        foreach (var itemGridPosition in mocusGridPositionToRemoveSet)
        {
            combatController.RemoveSkill(itemGridPosition);
            if (gridPosition2MocusSticked.TryGetValue(itemGridPosition, out var bomb))
            {
                if (bomb != null)
                {
                    bomb.SetActive(false);
                }
                gridPosition2MocusSticked.Remove(itemGridPosition);
            }
        }
    }

    private void DoEffect(Vector3Int gridPosition, GridUnit gridUnit = null)
    {
        if (gridUnit != null)
        {
            ApplyEffectInUnit(gridPosition, gridUnit);
            return;
        }

        var foes = combatController.GetGridUnitFoes(caster.tag);
        if (foes.Count == 0)
        {
            return;
        }
        foreach (var unit in foes)
        {
            if (unit.currentGridPosition != gridPosition)
            {
                continue;
            }
            ApplyEffectInUnit(gridPosition, unit);
        }
    }

    private void ApplyEffectInUnit(Vector3Int gridPosition, GridUnit gridUnit)
    {
        gridUnit.BlockMovement(secondsToBlockMovement);
        currentGridPosition = gridPosition;
        DoDamageInCurrentGridPosition();
        currentGridPosition = startGridPosition;
        mocusGridPositionToRemoveSet.Add(gridPosition);
        RemoveMocus();
    }

    private GameObject GetNewHitbox(Vector3 positionToUse)
    {
        if (hitbox == null)
        {
            Debug.LogError($"hitbox is NULL/EMPTY");
            return null;
        }

        return Instantiate(hitbox, positionToUse, hitbox.transform.rotation, transform);
    }
    private GameObject GetNewBomb(Vector3 positionToUse)
    {
        if (bombGameObject == null)
        {
            Debug.LogError($"hitbox is NULL/EMPTY");
            return null;
        }

        return Instantiate(bombGameObject, positionToUse, bombGameObject.transform.rotation, transform);
    }

    protected override void OnDestroy()
    {
        combatController.EventGridUnitEnter -= OnGridUnitEnter;
        foreach (var item in movingTweenMocusSet)
        {
            item.Kill();
        }
        movingTweenMocusSet.Clear();
        base.OnDestroy();
    }

}
