using UnityEngine;
using UnityEngine.Tilemaps;
public class CannonSkillBehaviour : SkillBehaviour
{
    [SerializeField] private SpriteRenderer thisSpriteRenderer;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition + directionToUse;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);
        DoDamageInTheRow();
        // Debug.Log($"Activate done. Calling Finish in 0.5f");
        // Invoke(nameof(Finish), 0.5f);
    }

    private void DoDamageInTheRow()
    {
        if (tilemap == null)
        {
            Debug.LogError($"tilemap is NULL/EMPTY");
            InvokeFinishAndDestroy();
            return;
        }
        MoveToNextPosition();
        while (tilemap.HasTile(currentGridPosition))
        {
            SpawnSomethingInCurrentGridPosition();
            if (
                // No existe una unidad ahi
                !combatController.TryGetGridUnitFromPosition(currentGridPosition, out var gridUnit)
                // Está en diferente altura (coordenada y)
                || gridUnit.currentGridPosition.y != currentGridPosition.y
                // El caster es igual al que se encuentra en esa posicion
                || gridUnit == caster
                // Los tag son iguales (enemy == enemy)
                || gridUnit.CompareTag(caster.tag)
                )
            {
                // Avanzar
                MoveToNextPosition();
                continue;
            }
            DoDamageInCurrentGridPosition();
            break;
        }
        Invoke(nameof(InvokeFinishAndDestroy), 1f);
    }

    private void SpawnSomethingInCurrentGridPosition()
    {
        var clone = Instantiate(thisSpriteRenderer, tilemap.GetCellCenterWorld(currentGridPosition), Quaternion.identity, transform);
    }

    private void MoveToNextPosition() => currentGridPosition += directionToUse;
}
