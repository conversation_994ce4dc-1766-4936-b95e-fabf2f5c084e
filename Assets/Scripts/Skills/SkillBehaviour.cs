using UnityEngine;
using UnityEngine.Tilemaps;
using System;
using DG.Tweening;

public abstract class SkillBehaviour : MonoBehaviour
{
    /// <summary>
    /// Has to be assigned via Initialize
    /// </summary>
    [HideInInspector] public SkillDataSO skillData;
    /// <summary>
    /// When the skill finishes but not necessary has to be destroyed
    /// </summary>
    public event Action<SkillBehaviour> EventFinish;

    protected Vector3Int startGridPosition;
    protected Vector3Int currentGridPosition;
    protected Vector3Int directionToUse;
    protected Tilemap tilemap;
    protected GridUnit caster;
    protected CombatController combatController;

    public virtual void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        directionToUse = caster.CompareTag("Enemy") ? Vector3Int.left : Vector3Int.right;
        this.startGridPosition = startGridPosition;
        currentGridPosition = startGridPosition;
        this.tilemap = tilemap;
        this.caster = caster;
        this.combatController = combatController;
        combatController.AddSkill(currentGridPosition, this);
    }

    public virtual void Initialize(SkillDataSO skillData) => this.skillData = skillData;

    protected void InvokeFinish() => EventFinish?.Invoke(this);

    /// <summary>
    /// Invokes event finish and destroy
    /// </summary>
    protected virtual void InvokeFinishAndDestroy()
    {
        EventFinish?.Invoke(this);
        Destroy(gameObject);
    }

    protected virtual bool DoDamageInCurrentGridPosition()
    {
        if (combatController.TryGetGridUnitFromPosition(currentGridPosition, out var gridUnit))
        {
            gridUnit.TakeDamage(skillData.amount);
            return true;
        }
        return false;
    }

    /// <summary>
    /// Cancel invokes, cancel tweens and Remove skill from combatController
    /// </summary>
    protected virtual void OnDestroy()
    {
        CancelInvoke();
        this.DOKill();
        combatController.RemoveSkill(this);
    }

}
