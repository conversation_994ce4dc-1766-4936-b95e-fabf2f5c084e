using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class BombSkillBehaviour : SkillBehaviour
{
    public float jumpValue = 1.5f;
    public int jumpsCount = 1;
    public GameObject explosionVFX;
    public GameObject bombGameObject;
    public GameObject hitboxGameObject;

    [SerializeField] private Vector3Int gridPositionToLand;
    [SerializeField] private Vector3 positionToLand;

    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition;
        gridPositionToLand = currentGridPosition + (directionToUse * 4);

        if (bombGameObject != null)
        {
            bombGameObject.transform.position = tilemap.GetCellCenterWorld(currentGridPosition);
        }
        else
        {
            Debug.LogError($"bombGameObject is NULL/EMPTY");
        }

        positionToLand = tilemap.GetCellCenterWorld(gridPositionToLand);
        if (hitboxGameObject != null)
        {
            hitboxGameObject.transform.position = positionToLand;
        }
        else
        {
            Debug.LogError($"hitboxGameObject is NULL/EMPTY");
        }

        if (bombGameObject != null)
        {
            bombGameObject.transform.DOJump(positionToLand, jumpValue, jumpsCount, skillData.secondsToRemainPerCell)
                .SetEase(Ease.Linear).SetLink(caster.gameObject)
                .onComplete += OnLand;
        }
        else
        {
            Debug.LogError($"bombGameObject is NULL/EMPTY");
        }
    }

    private void OnLand()
    {
        currentGridPosition = gridPositionToLand;
        DoDamageInCurrentGridPosition();

        if (explosionVFX != null)
        {
            explosionVFX.transform.position = positionToLand;
            explosionVFX.SetActive(true);
        }
        else
        {
            Debug.LogError($"explosionVFX is NULL/EMPTY");
        }
        Invoke(nameof(InvokeFinishAndDestroy), 0.5f);
    }

}
