using UnityEngine;
using UnityEngine.Tilemaps;

public class HealSelfSkillBehaviour : SkillBehaviour
{
    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        caster.TakeDamage(-skillData.amount);
        InvokeFinishAndDestroy();
    }

}
