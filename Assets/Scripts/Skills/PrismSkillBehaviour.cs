using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class PrismSkillBehaviour : SkillBehaviour
{
    public float shakeStrength = 5f;
    public float secondsToShake = 1f;
    public GameObject pathGameObject;
    public List<GameObject> pointsInThePath = new();

    public Queue<Vector3Int> gridPositionToLand = new();

    private Vector3 positionToLand;

    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);

        var nextFrontPosition = startGridPosition + directionToUse;
        var moveUpOrDown = Vector3Int.up;
        if (!CanCreatePath(nextFrontPosition, ref moveUpOrDown))
        {
            return;
        }

        CreatePath(nextFrontPosition, moveUpOrDown);

        positionToLand = tilemap.GetCellCenterWorld(gridPositionToLand.Peek());
        // transform.position = positionToLand;
        TweenToDestination();
    }

    private void CreatePath(Vector3Int nextFrontPosition, Vector3Int moveUpOrDown)
    {
        var nextPosition = nextFrontPosition;
        var emergencyExit = 30;
        while (tilemap.HasTile(nextFrontPosition))
        {
            if (emergencyExit-- <= 0)
            {
                break;
            }
            if (gridPositionToLand.Count % 2 != 1)
            {
                nextPosition = nextFrontPosition + moveUpOrDown;
            }
            else
            {
                nextPosition = nextFrontPosition;
            }
            gridPositionToLand.Enqueue(nextPosition);
            pointsInThePath.Add(Instantiate(pathGameObject, tilemap.GetCellCenterWorld(nextPosition), Quaternion.identity, transform));
            nextFrontPosition += directionToUse;
        }

        if (emergencyExit <= 0)
        {
            Debug.LogError($"Emergency exit couldn't finish the create path");
            InvokeFinishAndDestroy();
        }
    }

    private bool CanCreatePath(Vector3Int nextFrontPosition, ref Vector3Int moveUpOrDown)
    {
        var firstAttemptPosition = nextFrontPosition + moveUpOrDown;
        if (!tilemap.HasTile(firstAttemptPosition))
        {
            moveUpOrDown = Vector3Int.down;
            firstAttemptPosition = nextFrontPosition + moveUpOrDown;
            if (!tilemap.HasTile(firstAttemptPosition))
            {
                Debug.LogError($"Can't use {name} because up or down is null");
                InvokeFinishAndDestroy();
                return false;
            }
        }
        return true;
    }

    private void OnLand()
    {
        currentGridPosition = gridPositionToLand.Dequeue();
        combatController.UpdateSkill(this, currentGridPosition);
        combatController.UpdateGridUnit(caster, currentGridPosition);
        if (combatController.TryGetGridUnitFromPosition(currentGridPosition, out var unit))
        {
            if (!unit.CompareTag(caster.tag))
            {
                DoDamageInCurrentGridPosition();
            }
        }
        if (gridPositionToLand.Count == 0)
        {
            Invoke(nameof(InvokeFinishAndDestroy), skillData.secondsToRemainPerCell);
            return;
        }
        positionToLand = tilemap.GetCellCenterWorld(gridPositionToLand.Peek());
        // transform.position = positionToLand;
        Invoke(nameof(TweenToDestination), skillData.secondsToRemainPerCell);
    }

    private void TweenToDestination()
    {
        // Cancel any previous tween
        this.DOKill();

        Sequence seq = DOTween.Sequence();

        // Shake just in the first
        if (currentGridPosition == startGridPosition)
        {
            seq.Append(caster.transform.DOShakeRotation(secondsToShake, Vector3.forward * shakeStrength));
        }
        seq.Append(caster.transform.DOMove(positionToLand, skillData.secondsToRemainPerCell));
        seq.SetLink(caster.gameObject).SetEase(Ease.Linear).onComplete += OnLand;
    }

    protected override void InvokeFinishAndDestroy()
    {
        this.DOKill();
        CancelInvoke();

        caster.transform.position = tilemap.GetCellCenterWorld(startGridPosition);
        combatController.UpdateGridUnit(caster, startGridPosition);

        base.InvokeFinishAndDestroy();
    }

}
