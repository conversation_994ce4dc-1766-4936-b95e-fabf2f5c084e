using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class DislikeSkillBehaviour : SkillBehaviour
{
    public float jumpValue = 1.5f;
    public int jumpsCount = 1;
    public int amountOfGridPosition = 3;
    public  float shakeDurationInSeconds = 0.3f;
    public float strengthValue = 0.5f;
    public float secondsToWaitBeforeJumpingToNextTarget = 1f;

    public Stack<Vector3Int> gridPositionToLand = new();

    private Vector3 positionToLand;

    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);

        Vector3Int randomPosition = Vector3Int.one;
        for (int j = 1; j <= amountOfGridPosition; j++)
        {
            do
            {
                randomPosition = new Vector3Int(
                    Random.Range(combatController.topLeft.x, combatController.enemyTopLeft.x - 1),
                    Random.Range(combatController.topLeft.y, combatController.enemyBottomRight.y - 4),
                    0
                );
            } while (gridPositionToLand.Contains(randomPosition));
            gridPositionToLand.Push(randomPosition);
        }

        positionToLand = tilemap.GetCellCenterWorld(gridPositionToLand.Peek());
        transform.position = positionToLand;
        TweenToDestination();
    }

    private void OnLand()
    {
        ShakeCamera();
        currentGridPosition = gridPositionToLand.Pop();
        combatController.UpdateSkill(this, currentGridPosition);
        if (combatController.TryGetGridUnitFromPosition(currentGridPosition, out var unit))
        {
            if (!unit.CompareTag(caster.tag))
            {
                DoDamageInCurrentGridPosition();
            }
        }
        if (gridPositionToLand.Count == 0)
        {
            Invoke(nameof(InvokeFinishAndDestroy), secondsToWaitBeforeJumpingToNextTarget);
            return;
        }
        positionToLand = tilemap.GetCellCenterWorld(gridPositionToLand.Peek());
        transform.position = positionToLand;
        Invoke(nameof(TweenToDestination), secondsToWaitBeforeJumpingToNextTarget);
    }

    private void ShakeCamera()
    {
        MainCameraController.Instance.transform.DOShakePosition(shakeDurationInSeconds, new Vector3(strengthValue, strengthValue, 0));
    }

    private void TweenToDestination()
    {
        // Cancel any previous tween
        this.DOKill();

        Sequence seq = DOTween.Sequence();
        seq.Join(caster.transform.DOJump(positionToLand, jumpValue, jumpsCount, skillData.secondsToRemainPerCell));
        seq.SetLink(gameObject).SetEase(Ease.Linear).onComplete += OnLand;
    }

    protected override void InvokeFinishAndDestroy()
    {
        caster.transform.position = tilemap.GetCellCenterWorld(startGridPosition);
        this.DOKill();
        base.InvokeFinishAndDestroy();
    }

}
