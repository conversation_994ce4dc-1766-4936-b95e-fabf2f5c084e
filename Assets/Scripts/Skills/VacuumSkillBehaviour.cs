using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class VacuumSkillBehaviour : SkillBehaviour
{
    /// <summary>
    /// At the end of the skill, movement will be blocked
    /// </summary>
    [Tooltip("At the end of the skill, movement and action will be unblocked in x seconds")]
    public float secondsToUnblockMovement;
    public GameObject landingPointGameObject;
    public float shakeDurationInSeconds = 0.3f;
    public float strengthValue = 0.5f;

    [SerializeField] private SpriteRenderer thisSpriteRenderer;

    private Vector3 nextPosition;
    private Vector3Int nextGridPosition;
    private GridUnit grabbedUnit;
    private Vector3Int randomGridPositonForGrabberUnit;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);

        if (landingPointGameObject != null)
        {
            landingPointGameObject.SetActive(false);
        }
        else
        {
            Debug.LogError($"landingPointGameObject is NULL/EMPTY");
        }
        if (thisSpriteRenderer != null)
        {
            if (caster.CompareTag("Enemy"))
            {
                thisSpriteRenderer.flipX = true;
            }
        }
        currentGridPosition = startGridPosition + directionToUse;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);

        Invoke(nameof(DetectEnemyInRow), 0.5f);
    }

    private void DetectEnemyInRow()
    {
        if (tilemap == null)
        {
            Debug.LogError($"tilemap is NULL/EMPTY");
            InvokeFinishAndDestroy();
            return;
        }
        var foes = combatController.GetGridUnitFoes(caster.tag);
        grabbedUnit = null;
        var distanceToClosest = 100f;
        Vector3Int gridPositionClosesOpponent = new();
        foreach (var item in foes)
        {
            if (item.currentGridPosition.y != caster.currentGridPosition.y)
            {
                continue;
            }
            if (grabbedUnit != null)
            {
                var distance = Vector3Int.Distance(grabbedUnit.currentGridPosition, item.currentGridPosition);
                if (distance < distanceToClosest)
                {
                    grabbedUnit = item;
                    distanceToClosest = distance;
                    gridPositionClosesOpponent = item.currentGridPosition;
                }
            }
            else
            {
                grabbedUnit = item;
                gridPositionClosesOpponent = grabbedUnit.currentGridPosition;
            }
        }
        if (grabbedUnit != null)
        {
            DragClosestOpponent(grabbedUnit, gridPositionClosesOpponent);
        }
        else
        {
            Debug.Log($"{name} Didn't find anyone to grab");
            InvokeFinishAndDestroy();
        }
    }

    private void DragClosestOpponent(GridUnit closestOpponent, Vector3Int gridPositionClosesOpponent)
    {
        if (closestOpponent == null)
        {
            InvokeFinishAndDestroy();
            return;
        }
        currentGridPosition = gridPositionClosesOpponent;
        combatController.UpdateSkill(this, currentGridPosition);
        if (grabbedUnit.currentHealthPoint == 0)
        {
            InvokeFinishAndDestroy();
            return;
        }
        grabbedUnit.BlockMovementAndAction(10f);

        nextGridPosition = currentGridPosition;
        OnArriveNextGridPosition();
    }

    private void SetTweenToNextGridPosition()
    {
        grabbedUnit.transform.DOMove(nextPosition, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(caster.gameObject)
            .onComplete += OnArriveNextGridPosition;
    }

    private void OnArriveNextGridPosition()
    {
        currentGridPosition = nextGridPosition;

        combatController.UpdateSkill(this, currentGridPosition);
        if (grabbedUnit != null)
        {
            combatController.UpdateGridUnit(grabbedUnit, currentGridPosition);
            grabbedUnit.SnapToCell(currentGridPosition);
        }

        nextGridPosition = currentGridPosition - directionToUse;
        nextPosition = tilemap.GetCellCenterWorld(nextGridPosition);

        if (nextGridPosition == startGridPosition)
        {
            InvokeFinishAndDestroy();
            return;
        }

        var nextTile = tilemap.GetTile<OwnershipTileSO>(nextGridPosition);
        if (nextTile != null && caster.CompareTag(nextTile.TileOwner.ToString()))
        {
            // Next tile is opposite, stop the skill here
            DragToMeAndLaunch();
            return;
        }

        // Is next grid position valid?
        if (!tilemap.HasTile(nextGridPosition))
        {
            // Debug.Log($"No tile in nextGridPosition {nextGridPosition}");
            // Reach the end of the grid
            InvokeFinishAndDestroy();
            return;
        }
        SetTweenToNextGridPosition();
    }

    private void DragToMeAndLaunch()
    {
        var nextToCasterPosition = tilemap.GetCellCenterWorld(startGridPosition + directionToUse);
        grabbedUnit.transform.DOMove(nextToCasterPosition, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(caster.gameObject)
            .onComplete += OnNextToCaster;
    }

    private void OnNextToCaster()
    {
        // Choose random position
        var emergencyExit = 30;
        do
        {
            if (emergencyExit-- <= 0)
            {
                break;
            }
            randomGridPositonForGrabberUnit = combatController.Helper.GetRandomPlayerPositionInGrid();
        } while (!tilemap.HasTile(randomGridPositonForGrabberUnit));
        if (emergencyExit <= 0)
        {
            Debug.LogError($"emergencyExit");
            InvokeFinishAndDestroy();
            return;
        }
        combatController.RemoveGridUnit(grabbedUnit);
        Invoke(nameof(LaunchEnemy), 1.5f);
    }

    private void LaunchEnemy()
    {
        if (grabbedUnit == null)
        {
            Debug.LogError($"grabbedUnit is NULL/EMPTY");
            InvokeFinishAndDestroy();
            return;
        }
        var positionToLand = tilemap.GetCellCenterWorld(randomGridPositonForGrabberUnit);
        if (landingPointGameObject != null)
        {
            landingPointGameObject.transform.position = positionToLand;
            landingPointGameObject.SetActive(true);
        }
        else
        {
            Debug.LogError($"landingPointGameObject is NULL/EMPTY");
        }
        grabbedUnit.transform.DOJump(positionToLand, 2f, 1, 1f)
            .SetEase(Ease.Linear).SetLink(caster.gameObject)
            .onComplete += OnGrabbedUnitLand;
    }

    private void OnGrabbedUnitLand()
    {
        if (grabbedUnit == null)
        {
            Debug.LogError($"grabbedUnit is NULL/EMPTY");
            InvokeFinishAndDestroy();
            return;
        }
        currentGridPosition = tilemap.WorldToCell(grabbedUnit.transform.position);
        if (grabbedUnit != null)
        {
            grabbedUnit.BlockMovementAndAction(secondsToUnblockMovement);
            combatController.AddGridUnit(currentGridPosition, grabbedUnit);
            grabbedUnit.SnapToCell(currentGridPosition);
        }
        DoDamageInCurrentGridPosition();
        ShakeCamera();
        Invoke(nameof(InvokeFinishAndDestroy), 1f);
    }

    private void ShakeCamera()
    {
        MainCameraController.Instance.transform.DOShakePosition(shakeDurationInSeconds, new Vector3(strengthValue, strengthValue, 0));
    }
}
