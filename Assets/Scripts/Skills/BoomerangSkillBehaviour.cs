using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

/// <summary>
/// Attacks the first opponent in the row
/// </summary>
public class BoomerangSkillBehaviour : SkillBehaviour
{
    public float secondsToFullRotation;
    public float rotationValue;
    /// <summary>
    /// At the end of the skill, movement will be blocked
    /// </summary>
    [Tooltip("At the end of the skill, movement will be blocked")]
    public float secondsToUnblockMovement;

    private Vector3 nextPosition;
    private Vector3Int nextGridPosition;
    private HashSet<GridUnit> grabbedUnits = new();
    private HashSet<GridUnit> affectedUnits = new();

    private enum HeadingDirection
    {
        Forward = 0,
        Backward = 1,
    }

    private HeadingDirection headingDirection = HeadingDirection.Forward;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition + directionToUse;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);

        nextGridPosition = currentGridPosition + base.directionToUse;
        nextPosition = tilemap.GetCellCenterWorld(nextGridPosition);
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        transform.DORotate(Vector3.forward * rotationValue, secondsToFullRotation, RotateMode.FastBeyond360)
            .SetEase(Ease.Linear).SetLoops(-1).SetLink(gameObject);
        SetTweenToNextGridPosition();
    }

    private void SetTweenToNextGridPosition()
    {
        transform.DOMove(nextPosition, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(gameObject)
            .onComplete += OnArriveNextGridPosition;
        foreach (var gridUnit in grabbedUnits)
        {
            gridUnit.transform.DOMove(nextPosition, skillData.secondsToRemainPerCell)
                .SetEase(Ease.Linear).SetLink(gameObject);
        }
    }

    private void OnArriveNextGridPosition()
    {
        currentGridPosition = nextGridPosition;
        if (combatController.TryGetGridUnitFromPosition(currentGridPosition, out var gridUnit))
        {
            if (!grabbedUnits.Contains(gridUnit))
            {
                base.DoDamageInCurrentGridPosition();
                // Grab units that are still alive
                if (gridUnit != null && gridUnit.currentHealthPoint > 0)
                {
                    grabbedUnits.Add(gridUnit);
                    gridUnit.BlockMovement(10f);
                    affectedUnits.Add(gridUnit);
                }
            }

        }

        combatController.UpdateSkill(this, currentGridPosition);
        foreach (var item in grabbedUnits)
        {
            combatController.UpdateGridUnit(item, currentGridPosition);
            item.SnapToCell(currentGridPosition);
        }

        nextGridPosition = currentGridPosition + base.directionToUse;
        nextPosition = tilemap.GetCellCenterWorld(nextGridPosition);

        if (headingDirection == HeadingDirection.Backward)
        {
            if (nextGridPosition == startGridPosition)
            {
                InvokeFinishAndDestroy();
                return;
            }

            var nextTile = tilemap.GetTile<OwnershipTileSO>(nextGridPosition);
            var casterTag = caster.tag;
            if (nextTile != null && caster.CompareTag(nextTile.TileOwner.ToString()))
            {
                var unitsToReleaseSet = new HashSet<GridUnit>();
                // Next tile is opposite, check grabbed units.
                foreach (var gridUnitToCheck in grabbedUnits)
                {
                    if (!gridUnitToCheck.CompareTag(casterTag))
                    {
                        unitsToReleaseSet.Add(gridUnitToCheck);
                    }
                }
                foreach (var gridUnitToRelease in unitsToReleaseSet)
                {
                    grabbedUnits.Remove(gridUnitToRelease);
                }
            }
        }

        // Is next grid position valid?
        if (!tilemap.HasTile(nextGridPosition))
        {
            // Debug.Log($"No tile in nextGridPosition {nextGridPosition}");
            // Reach the end of the grid

            // If going forward, now goes backwards
            if (headingDirection == HeadingDirection.Forward)
            {
                SetBackwardMovementAndContinue();
                return;
            }

            // Is going backwards and finished
            InvokeFinishAndDestroy();
            return;
        }
        SetTweenToNextGridPosition();
    }

    private void SetBackwardMovementAndContinue()
    {
        headingDirection = HeadingDirection.Backward;
        directionToUse *= -1;
        nextPosition += directionToUse;
        SetTweenToNextGridPosition();
    }

    override protected void InvokeFinishAndDestroy()
    {
        foreach (var gridUnit in grabbedUnits)
        {
            gridUnit.BlockMovement(secondsToUnblockMovement);
        }
        foreach (var gridUnit in affectedUnits)
        {
            gridUnit.BlockMovement(secondsToUnblockMovement);
        }
        grabbedUnits.Clear();
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        base.InvokeFinishAndDestroy();
    }
}
