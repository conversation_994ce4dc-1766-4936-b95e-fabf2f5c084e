using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Tilemaps;

public class ExplodeWhenCellIsTouchedSkillBehaviour : SkillBehaviour
{
    private const int kAmountOfCells = 3;

    [SerializeField] private GameObject explosion;
    [SerializeField] private GameObject hitbox;
    public float secondsToShowHitboxes = 0.5f;
    public float secondsAfterExplosionToFinish = 0.5f;

    private HashSet<Vector3Int> positionsToDamage = new();
    // private Dictionary<Vector3, GameObject> hitboxesCreated = new();
    private HashSet<GameObject> hitboxesSet = new();
    private HashSet<GameObject> explosionsSet = new();
    private bool isInCooldown = false;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);
        CreateAndTrack();
        combatController.EventGridUnitEnter += OnGridUnityEnter;

        RunFirstTime();
    }

    private void RunFirstTime()
    {
        if (IsEnemyOnHitboxes())
        {
            ShowHitboxes();
        }
    }

    private bool IsEnemyOnHitboxes()
    {
        foreach (var item in positionsToDamage)
        {
            if (!combatController.TryGetGridUnitFromPosition(item, out var gridUnit))
            {
                continue;
            }
            // Ignore allies
            if (IsAlly(gridUnit))
            {
                continue;
            }

            return true;
        }
        return false;
    }

    private void OnGridUnityEnter(GridUnit unit, Vector3Int gridPosition)
    {
        if (isInCooldown)
        {
            return;
        }
        if (!positionsToDamage.Contains(gridPosition))
        {
            return;
        }
        // Ignore allies
        if (IsAlly(unit))
        {
            return;
        }
        ShowHitboxes();
    }

    private bool IsAlly(GridUnit unit) => unit.CompareTag(caster.tag);

    private void ShowHitboxes()
    {
        isInCooldown = true;
        ChangeActiveStateToList(hitboxesSet, true);
        Invoke(nameof(ShowExplotions), secondsToShowHitboxes);
    }

    private void ShowExplotions()
    {
        ChangeActiveStateToList(hitboxesSet, false);
        ChangeActiveStateToList(explosionsSet, true);
        ExecuteTheDamage();
        Invoke(nameof(DeactivateExplosion), secondsAfterExplosionToFinish);
    }

    private void ExecuteTheDamage()
    {
        foreach (var item in positionsToDamage)
        {
            if (!combatController.TryGetGridUnitFromPosition(item, out var gridUnit))
            {
                continue;
            }
            // Ignore allies
            if (IsAlly(gridUnit))
            {
                continue;
            }
            currentGridPosition = item;
            DoDamageInCurrentGridPosition();
        }
    }

    private void DeactivateExplosion()
    {
        ChangeActiveStateToList(explosionsSet, false);
        isInCooldown = false;
        RunFirstTime();
    }

    private void ChangeActiveStateToList(HashSet<GameObject> list, bool newState)
    {
        foreach (var item in list)
        {
            item.SetActive(newState);
        }
    }

    private void CreateAndTrack()
    {
        var bottomLeft = currentGridPosition + new Vector3Int(-1, -1, 0);
        for (int j = 0; j < kAmountOfCells; j++)
        {
            for (int k = 0; k < kAmountOfCells; k++)
            {
                var thePosition = bottomLeft + new Vector3Int(j, k, 0);
                if (!tilemap.HasTile(thePosition))
                {
                    continue;
                }
                positionsToDamage.Add(thePosition);
                var hitboxClone = Instantiate(hitbox, tilemap.GetCellCenterWorld(thePosition), Quaternion.identity, transform);
                hitboxClone.SetActive(false);
                hitboxesSet.Add(hitboxClone);
                var explosionClone = Instantiate(explosion, tilemap.GetCellCenterWorld(thePosition), Quaternion.identity, transform);
                explosionClone.SetActive(false);
                explosionsSet.Add(explosionClone);
            }
        }
    }

    protected override void OnDestroy()
    {
        CancelInvoke();
        base.OnDestroy();
    }

}
