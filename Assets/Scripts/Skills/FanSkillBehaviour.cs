using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class FanSkillBehaviour : SkillBehaviour
{
    /// <summary>
    /// At the end of the skill, movement will be blocked
    /// </summary>
    [Tooltip("At the end of the skill, movement and action will be unblocked in x seconds")]
    public float secondsToUnblockMovement;

    [SerializeField] private SpriteRenderer thisSpriteRenderer;

    private Vector3 nextPosition;
    private Vector3Int nextGridPosition;
    private GridUnit grabbedUnit;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        if (thisSpriteRenderer != null)
        {
            if (caster.CompareTag("Enemy"))
            {
                thisSpriteRenderer.flipX = true;
            }
        }
        currentGridPosition = startGridPosition + directionToUse;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);

        Invoke(nameof(DetectEnemyInRow), 0.5f);
    }

    private void DetectEnemyInRow()
    {
        if (tilemap == null)
        {
            Debug.LogError($"tilemap is NULL/EMPTY");
            InvokeFinishAndDestroy();
            return;
        }
        var foes = combatController.GetGridUnitFoes(caster.tag);
        grabbedUnit = null;
        foreach (var item in foes)
        {
            if (item.currentGridPosition.y != caster.currentGridPosition.y)
            {
                continue;
            }
            grabbedUnit = item;
            break;
        }
        if (grabbedUnit != null)
        {
            PushToBack();
        }
        else
        {
            Debug.Log($"{name} Didn't find anyone to grab");
            InvokeFinishAndDestroy();
        }
    }

    private void PushToBack()
    {
        currentGridPosition = grabbedUnit.currentGridPosition;
        combatController.RemoveSkill(this);
        DoDamageInCurrentGridPosition();
        if (grabbedUnit.currentHealthPoint == 0)
        {
            InvokeFinishAndDestroy();
            return;
        }
        grabbedUnit.BlockMovementAndAction(10f);

        currentGridPosition.x = combatController.topLeft.x;
        nextGridPosition = currentGridPosition;
        nextPosition = tilemap.GetCellCenterWorld(nextGridPosition);
        MoveGrabbedUnitUsingTween();
    }

    private void MoveGrabbedUnitUsingTween()
    {
        grabbedUnit.transform.DOMove(nextPosition, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(gameObject)
            .onComplete += OnArriveGridPosition;
    }

    private void OnArriveGridPosition()
    {
        currentGridPosition = nextGridPosition;

        if (grabbedUnit != null)
        {
            combatController.UpdateGridUnit(grabbedUnit, currentGridPosition);
            grabbedUnit.SnapToCell(currentGridPosition);
        }

        InvokeFinishAndDestroy();
    }

    override protected void InvokeFinishAndDestroy()
    {
        if (grabbedUnit != null)
        {
            grabbedUnit.BlockMovementAndAction(secondsToUnblockMovement);
        }
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        base.InvokeFinishAndDestroy();
    }
}
