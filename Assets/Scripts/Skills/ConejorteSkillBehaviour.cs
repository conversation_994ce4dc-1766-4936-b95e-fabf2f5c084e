using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class ConejorteSkillBehaviour : SkillBehaviour
{
    public float jumpValue = 1.5f;
    public int jumpsCount = 1;
    public GameObject hitboxGameObject;
    public GameObject bombGameObject;
    public GameObject explosionVFX;
    public float secondsBetweenThrows = 1.5f;

    [SerializeField] private Dictionary<Vector3Int, GameObject> gridPosition2BombHidden = new();
    [SerializeField] private Dictionary<Vector3Int, GameObject> gridPosition2TravelingBomb = new();
    [SerializeField] private HashSet<Vector3Int> bombGridPositionToRemoveSet = new();
    [SerializeField] private float secondsToBlockMovement = 2f;
    [SerializeField] private Vector3Int playerGridPosition;

    private HashSet<Sequence> movingTweenMocusSet = new();

    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        combatController.RemoveSkill(this);
        hitboxGameObject.SetActive(false);
        bombGameObject.SetActive(false);
        caster.EventDied += OnCasterDie;

        Invoke(nameof(ThrowBombitaToRandom), secondsBetweenThrows);
    }

    private void ThrowBombitaToRandom()
    {
        Vector3Int randomGridPosition = combatController.Helper.GetRandomPlayerPositionInGrid();
        ThrowBombita(randomGridPosition);
    }

    private void ThrowBombita(Vector3Int gridPositionToLand)
    {
        var positionToLand = tilemap.GetCellCenterWorld(gridPositionToLand);
        if (hitboxGameObject == null)
        {
            Debug.LogError($"hitboxGameObject is NULL/EMPTY");
            return;
        }
        if (bombGameObject == null)
        {
            Debug.LogError($"bombGameObject is NULL/EMPTY");
            return;
        }

        hitboxGameObject.transform.position = positionToLand;
        if (explosionVFX != null)
        {
            explosionVFX.transform.position = positionToLand;
        }
        bombGameObject.transform.position = tilemap.GetCellCenterWorld(currentGridPosition);

        hitboxGameObject.SetActive(true);
        bombGameObject.SetActive(true);

        Sequence tween = bombGameObject.transform.DOJump(positionToLand, jumpValue, jumpsCount, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(caster.gameObject)
        ;
        tween.onComplete += () => OnLand(hitboxGameObject, bombGameObject, gridPositionToLand, tween);
        movingTweenMocusSet.Add(tween);
        gridPosition2TravelingBomb[gridPositionToLand] = bombGameObject;
    }

    private void OnLand(GameObject hitboxGameObject, GameObject bombGameObject, Vector3Int gridPositionToLand, Sequence tween)
    {
        movingTweenMocusSet.Remove(tween);
        hitboxGameObject.SetActive(false);
        gridPosition2BombHidden.Add(gridPositionToLand, bombGameObject);
        combatController.EventGridUnitEnter += OnGridUnitEnter;
        combatController.AddSkill(gridPositionToLand, this);
        gridPosition2TravelingBomb.Remove(gridPositionToLand);
        Invoke(nameof(HideBombita), 1f);
        DoEffect(gridPositionToLand);
        InvokeFinish();
    }

    private void HideBombita()
    {
        if (bombGameObject == null)
        {
            return;
        }
        bombGameObject.SetActive(false);
    }

    private void OnGridUnitEnter(GridUnit unit, Vector3Int gridPosition)
    {
        if (gridPosition2BombHidden.Count == 0)
        {
            return;
        }
        if (!gridPosition2BombHidden.ContainsKey(gridPosition))
        {
            return;
        }
        if (unit.CompareTag(caster.tag))
        {
            // Ignoring allies
            return;
        }
        DoEffect(gridPosition, unit);
    }

    private void RemoveBombita()
    {
        foreach (var itemGridPosition in bombGridPositionToRemoveSet)
        {
            combatController.RemoveSkill(itemGridPosition);
            if (gridPosition2BombHidden.TryGetValue(itemGridPosition, out var bomb))
            {
                if (bomb != null)
                {
                    bomb.SetActive(false);
                }
                gridPosition2BombHidden.Remove(itemGridPosition);
            }
        }
        Destroy(gameObject);
    }

    private void DoEffect(Vector3Int gridPosition, GridUnit gridUnit = null)
    {
        if (gridUnit != null)
        {
            ApplyEffectInUnit(gridPosition, gridUnit);
            return;
        }

        var foes = combatController.GetGridUnitFoes(caster.tag);
        if (foes.Count == 0)
        {
            return;
        }
        foreach (var unit in foes)
        {
            if (unit.currentGridPosition != gridPosition)
            {
                continue;
            }
            ApplyEffectInUnit(gridPosition, unit);
        }
    }

    private void ApplyEffectInUnit(Vector3Int gridPosition, GridUnit gridUnit)
    {
        gridUnit.BlockMovement(secondsToBlockMovement);
        currentGridPosition = gridPosition;
        if (explosionVFX != null)
        {
            explosionVFX.SetActive(true);
        }
        else
        {
            Debug.LogError($"explosionVFX is NULL/EMPTY", gameObject);
        }
        DoDamageInCurrentGridPosition();
        currentGridPosition = startGridPosition;
        bombGridPositionToRemoveSet.Add(gridPosition);
        Invoke(nameof(RemoveBombita), 1f);
    }

    private void OnCasterDie(GridUnit unit) => Destroy(gameObject);

    protected override void OnDestroy()
    {
        combatController.EventGridUnitEnter -= OnGridUnitEnter;
        if (caster != null)
        {
            caster.EventDied -= OnCasterDie;
        }
        foreach (var item in movingTweenMocusSet)
        {
            item.Kill();
        }
        movingTweenMocusSet.Clear();
        base.OnDestroy();
    }

}
