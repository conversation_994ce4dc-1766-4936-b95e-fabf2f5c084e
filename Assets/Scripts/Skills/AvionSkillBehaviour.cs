using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class AvionSkillBehaviour : SkillBehaviour
{
    public GameObject hitbox;
    public List<Vector3> pathPositions = new();
    public List<Vector3Int> pathGridPositions = new();

    private int index = -1;

    override public void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);

        ProcessPath();
        if (pathPositions.Count == 0)
        {
            return;
        }

        Invoke(nameof(OnArriveNextGridPosition), 1f);
    }

    private void ProcessPath()
    {
        var gridPosition = startGridPosition;
        var lastGridPosition = startGridPosition;
        var emergencyExit = 30;
        var isGoingUp = true;
        while (gridPosition.x >= combatController.topLeft.x)
        {
            if (emergencyExit <= 0)
            {
                break;
            }
            gridPosition = lastGridPosition + directionToUse + (isGoingUp ? Vector3Int.up : Vector3Int.down);
            if (!tilemap.HasTile(gridPosition))
            {
                isGoingUp = !isGoingUp;
                continue;
            }

            var positionToUse = tilemap.GetCellCenterWorld(gridPosition);
            SpawnHitbox(positionToUse);
            pathPositions.Add(positionToUse);
            pathGridPositions.Add(gridPosition);
            lastGridPosition = gridPosition;
        }
        if (emergencyExit <= 0)
        {
            Debug.LogError($"emergencyExit");
            pathPositions.Clear();
            pathGridPositions.Clear();
            return;
        }

    }

    private void SpawnHitbox(Vector3 positionToUse)
    {
        if (hitbox == null)
        {
            Debug.LogError($"hitbox is NULL/EMPTY");
            return;
        }
        
        Instantiate(hitbox, positionToUse, hitbox.transform.rotation, transform);
    }

    private void SetTweenToNextGridPosition()
    {
        if (index >= pathPositions.Count)
        {
            InvokeFinishAndDestroy();
            return;
        }
        var nextPosition = pathPositions[index];

        caster.transform.DOMove(nextPosition, skillData.secondsToRemainPerCell)
            .SetEase(Ease.Linear).SetLink(caster.gameObject)
            .onComplete += OnArriveNextGridPosition;
    }

    private void OnArriveNextGridPosition()
    {
        index++;
        if (index < pathPositions.Count)
        {
            var gridPositionToUse = pathGridPositions[index];
            caster.SnapToCell(gridPositionToUse);
            combatController.UpdateGridUnit(caster, gridPositionToUse);
        }
        Invoke(nameof(SetTweenToNextGridPosition), skillData.secondsToRemainPerCell);
    }

    protected override void InvokeFinishAndDestroy()
    {
        this.DOKill();
        caster.SnapToCell(startGridPosition);
        base.InvokeFinishAndDestroy();
    }

}
