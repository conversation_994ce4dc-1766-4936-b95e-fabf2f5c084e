using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Tilemaps;
public class ExplodeSkillBehaviour : SkillBehaviour
{
    private const int kAmountOfCells = 3;
    [SerializeField] private GameObject explosion;
    [SerializeField] private GameObject hitbox;
    public float secondsToShowHitboxes = 0.5f;
    public float secondsAfterExplosionToFinish = 0.5f;


    private List<Vector3Int> positionsToDamage = new();
    private Dictionary<Vector3, GameObject> hitboxesCreated = new();

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);

        ShowHitboxes();
    }

    private void ShowExplotions()
    {
        foreach (var gridPosition in positionsToDamage)
        {
            Instantiate(explosion, tilemap.GetCellCenterWorld(gridPosition), Quaternion.identity, transform);
            if (hitboxesCreated.TryGetValue(gridPosition, out GameObject hitbox))
            {
                if (hitbox != null)
                {
                    hitbox.SetActive(false);
                }
            }
            if (!combatController.TryGetGridUnitFromPosition(gridPosition, out var gridUnit))
            {
                continue;
            }
            // Ignore allies
            if (gridUnit.CompareTag(caster.tag))
            {
                continue;
            }
            currentGridPosition = gridPosition;
            DoDamageInCurrentGridPosition();
        }
        Invoke(nameof(InvokeFinishAndDestroy), secondsAfterExplosionToFinish);
    }

    private void ShowHitboxes()
    {
        var bottomLeft = currentGridPosition + new Vector3Int(-1, -1, 0);
        for (int j = 0; j < kAmountOfCells; j++)
        {
            for (int k = 0; k < kAmountOfCells; k++)
            {
                var thePosition = bottomLeft + new Vector3Int(j, k, 0);
                if (!tilemap.HasTile(thePosition))
                {
                    continue;
                }
                positionsToDamage.Add(thePosition);
                var clone = Instantiate(hitbox, tilemap.GetCellCenterWorld(thePosition), Quaternion.identity, transform);
                hitboxesCreated[thePosition] = clone;
            }
        }
        Invoke(nameof(ShowExplotions), secondsToShowHitboxes);
    }

}
