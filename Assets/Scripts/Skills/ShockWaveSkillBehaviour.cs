using UnityEngine;
using UnityEngine.Tilemaps;

/// <summary>
/// Moves one tile horizontally per second till hitting something or reaching end of grid. Disappear on contact or end of grid.
/// </summary>
public class ShockWaveSkillBehaviour : SkillBehaviour
{
    private Vector3Int nextGridPosition;

    public override void Activate(Vector3Int startGridPosition, Tilemap tilemap, GridUnit caster, CombatController combatController)
    {
        base.Activate(startGridPosition, tilemap, caster, combatController);
        currentGridPosition = startGridPosition + directionToUse;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);
        combatController.UpdateSkill(this, currentGridPosition);
        combatController.EventGridUnitEnter += OnUnityEnterGridPosition;

        nextGridPosition = currentGridPosition + directionToUse;
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        TimerToNextJumpGridPosition();
    }

    private void OnUnityEnterGridPosition(GridUnit unit, Vector3Int gridPosition)
    {
        if (gridPosition != currentGridPosition)
        {
            return;
        }
        DoDamageInCurrentGridPosition();
    }

    private void TimerToNextJumpGridPosition() => Invoke(nameof(OnArriveNextGridPosition), skillData.secondsToRemainPerCell);

    private void OnArriveNextGridPosition()
    {
        currentGridPosition = nextGridPosition;
        transform.position = tilemap.GetCellCenterWorld(currentGridPosition);
        // Is current grid position valid?
        if (!tilemap.HasTile(currentGridPosition))
        {
            // Debug.Log($"No tile in currentGridPosition {currentGridPosition}");
            // Reach the end of the grid
            InvokeFinishAndDestroy();
            return;
        }
        
        if (combatController.TryGetGridUnitFromPosition(currentGridPosition, out var gridUnit))
        {
            if (!caster.CompareTag(gridUnit.tag))
            {
                // If opponent is different, damage
                DoDamageInCurrentGridPosition();
            }
        }
        combatController.UpdateSkill(this, currentGridPosition);

        nextGridPosition = currentGridPosition + directionToUse;
        TimerToNextJumpGridPosition();
    }

    override protected void InvokeFinishAndDestroy()
    {
        CancelInvoke();
        combatController.EventGridUnitEnter -= OnUnityEnterGridPosition;
        // Debug.Log(string.Join(",", combatController.GetGridUnitFoes(caster.tag)));
        base.InvokeFinishAndDestroy();
    }

}
