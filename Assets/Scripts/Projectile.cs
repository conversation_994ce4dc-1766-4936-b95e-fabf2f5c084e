using UnityEngine;

public class Projectile : MonoBehaviour
{
    [Header("Projectile Settings")]
    [SerializeField] private float lifetime = 3f;
    [SerializeField] private int damage = 1;
    [SerializeField] private bool destroyOnHit = true;
    [SerializeField] private GameObject hitEffectPrefab;
    
    [Header("Visual Settings")]
    [SerializeField] private Trail<PERSON>enderer trail;
    
    // Private variables
    private GameObject shooter;
    private Vector2 direction;
    
    private void Start()
    {
        // Destroy the projectile after its lifetime
        Destroy(gameObject, lifetime);
    }
    
    public void Initialize(GameObject shooter, Vector2 direction)
    {
        this.shooter = shooter;
        this.direction = direction;
        
        // Rotate the projectile to face the direction of travel
        float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
    }
    
    private void OnTriggerEnter2D(Collider2D collision)
    {
        // Ignore collisions with the shooter
        if (shooter != null && collision.gameObject == shooter)
        {
            return;
        }
        
        // Check if we hit something that can take damage
        IDamageable damageable = collision.GetComponent<IDamageable>();
        if (damageable != null)
        {
            damageable.TakeDamage(damage);
        }
        
        // Spawn hit effect if available
        if (hitEffectPrefab != null)
        {
            Instantiate(hitEffectPrefab, transform.position, Quaternion.identity);
        }
        
        // Destroy the projectile if set to destroy on hit
        if (destroyOnHit)
        {
            Destroy(gameObject);
        }
    }
    
    // For objects that don't have triggers
    private void OnCollisionEnter2D(Collision2D collision)
    {
        // Ignore collisions with the shooter
        if (shooter != null && collision.gameObject == shooter)
        {
            return;
        }
        
        // Check if we hit something that can take damage
        IDamageable damageable = collision.gameObject.GetComponent<IDamageable>();
        if (damageable != null)
        {
            damageable.TakeDamage(damage);
        }
        
        // Spawn hit effect if available
        if (hitEffectPrefab != null)
        {
            Instantiate(hitEffectPrefab, transform.position, Quaternion.identity);
        }
        
        // Destroy the projectile if set to destroy on hit
        if (destroyOnHit)
        {
            Destroy(gameObject);
        }
    }
}
