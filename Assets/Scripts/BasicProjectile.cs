using UnityEngine;

[RequireComponent(typeof(Projectile))]
[RequireComponent(typeof(SpriteRenderer))]
[RequireComponent(typeof(CircleCollider2D))]
public class BasicProjectile : MonoBehaviour
{
    [Header("Visual Settings")]
    [SerializeField] private Color projectileColor = Color.cyan;
    [SerializeField] private float projectileSize = 0.3f;
    [SerializeField] private bool addTrail = true;
    [SerializeField] private Color trailColor = Color.cyan;
    [SerializeField] private float trailTime = 0.5f;
    
    private SpriteRenderer spriteRenderer;
    private CircleCollider2D circleCollider;
    private TrailRenderer trailRenderer;
    
    private void Awake()
    {
        // Get or add required components
        spriteRenderer = GetComponent<SpriteRenderer>();
        circleCollider = GetComponent<CircleCollider2D>();
        
        // Set up sprite renderer
        if (spriteRenderer != null)
        {
            spriteRenderer.color = projectileColor;
            transform.localScale = new Vector3(projectileSize, projectileSize, 1f);
        }
        
        // Set up collider
        if (circleCollider != null)
        {
            circleCollider.radius = 0.5f; // Standard size for Unity circle sprite
            circleCollider.isTrigger = true;
        }
        
        // Add trail if requested
        if (addTrail && GetComponent<TrailRenderer>() == null)
        {
            trailRenderer = gameObject.AddComponent<TrailRenderer>();
            trailRenderer.time = trailTime;
            trailRenderer.startWidth = projectileSize * 0.8f;
            trailRenderer.endWidth = 0f;
            trailRenderer.startColor = trailColor;
            trailRenderer.endColor = new Color(trailColor.r, trailColor.g, trailColor.b, 0f);
            trailRenderer.material = new Material(Shader.Find("Sprites/Default"));
        }
    }
}
