using System;
using DG.Tweening;
using UnityEngine;

namespace OhMyAnt.Controllers
{
	public class BarController : MonoBehaviour
	{
		[SerializeField] private GameObject pivot = default;
		[SerializeField] private SpriteRenderer barRenderer = default;
		[SerializeField] private SpriteRenderer background = default;
		[SerializeField] private float secondsToFill;

		public ListOfSkillsUI listOfSkillsUI;

		private void OnValidate()
		{
			if (pivot == null)
			{
				Debug.LogError($"{name} {nameof(pivot)} IS NULL !!!");
			}
			if (barRenderer == null)
			{
				Debug.LogError($"{name} {nameof(barRenderer)} IS NULL !!!");
			}
			if (background == null)
			{
				Debug.LogError($"{name} {nameof(background)} IS NULL !!!");
			}
		}

		void OnEnable()
		{
			if (listOfSkillsUI != null)
			{
				listOfSkillsUI.EventClose += OnListOfSkillsClose;
			}
			Show();
		}

		public void Show()
		{
			var localScale = pivot.transform.localScale;
			pivot.transform.localScale = new Vector3(0, localScale.y, localScale.z);
			pivot.transform.DOScaleX(1f, secondsToFill).SetEase(Ease.Linear).SetLink(gameObject).onComplete += OnBarComplete;
		}

		private void OnBarComplete()
		{
			if (listOfSkillsUI == null)
			{
				Debug.LogError($"listOfSkillsUI is NULL/EMPTY");
				return;
			}
			listOfSkillsUI.Show();
		}

        private void OnListOfSkillsClose(ListOfSkillsUI uI) => Show();

    }
}
