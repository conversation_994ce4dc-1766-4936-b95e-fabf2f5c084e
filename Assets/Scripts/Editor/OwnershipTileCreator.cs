using UnityEngine;
using UnityEditor;

public class OwnershipTileSOCreator : EditorWindow
{
    private Sprite tileSprite;
    private OwnershipTileSO.Owner tileOwner = OwnershipTileSO.Owner.None;
    private string tileName = "New Ownership Tile";
    private Color playerColor = new Color(0.2f, 0.7f, 1f, 1f);  // Blue for player
    private Color enemyColor = new Color(1f, 0.3f, 0.3f, 1f);   // Red for enemy
    private Color neutralColor = new Color(1f, 1f, 1f, 1f);     // White for neutral

    [MenuItem("Tools/Ownership Tile Creator")]
    public static void ShowWindow()
    {
        GetWindow<OwnershipTileSOCreator>("Ownership Tile Creator");
    }

    private void OnGUI()
    {
        GUILayout.Label("Create Ownership Tile", EditorStyles.boldLabel);

        tileSprite = (Sprite)EditorGUILayout.ObjectField("Tile Sprite", tileSprite, typeof(Sprite), false);
        tileName = EditorGUILayout.TextField("Tile Name", tileName);
        tileOwner = (OwnershipTileSO.Owner)EditorGUILayout.EnumPopup("Tile Owner", tileOwner);

        EditorGUILayout.Space();
        GUILayout.Label("Colors", EditorStyles.boldLabel);
        playerColor = EditorGUILayout.ColorField("Player Color", playerColor);
        enemyColor = EditorGUILayout.ColorField("Enemy Color", enemyColor);
        neutralColor = EditorGUILayout.ColorField("Neutral Color", neutralColor);

        EditorGUILayout.Space();
        if (GUILayout.Button("Create Tile"))
        {
            CreateOwnershipTileSO();
        }
    }

    private void CreateOwnershipTileSO()
    {
        if (tileSprite == null)
        {
            EditorUtility.DisplayDialog("Error", "Please assign a sprite for the tile.", "OK");
            return;
        }

        // Create the tile asset
        OwnershipTileSO tile = ScriptableObject.CreateInstance<OwnershipTileSO>();
        
        // Set the sprite
        SerializedObject serializedTile = new SerializedObject(tile);
        SerializedProperty spriteProp = serializedTile.FindProperty("m_Sprite");
        spriteProp.objectReferenceValue = tileSprite;
        
        // Set the owner
        SerializedProperty ownerProp = serializedTile.FindProperty("tileOwner");
        ownerProp.enumValueIndex = (int)tileOwner;
        
        // Set the colors
        SerializedProperty playerColorProp = serializedTile.FindProperty("playerColor");
        if (playerColorProp != null) playerColorProp.colorValue = playerColor;
        
        SerializedProperty enemyColorProp = serializedTile.FindProperty("enemyColor");
        if (enemyColorProp != null) enemyColorProp.colorValue = enemyColor;
        
        SerializedProperty neutralColorProp = serializedTile.FindProperty("neutralColor");
        if (neutralColorProp != null) neutralColorProp.colorValue = neutralColor;
        
        serializedTile.ApplyModifiedProperties();

        // Create the asset file
        string path = EditorUtility.SaveFilePanelInProject(
            "Save Ownership Tile",
            tileName,
            "asset",
            "Save the ownership tile asset"
        );

        if (path.Length > 0)
        {
            AssetDatabase.CreateAsset(tile, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            EditorUtility.FocusProjectWindow();
            Selection.activeObject = tile;
            EditorUtility.DisplayDialog("Success", "Ownership Tile created successfully!", "OK");
        }
    }
}
