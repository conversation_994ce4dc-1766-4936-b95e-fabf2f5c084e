using UnityEngine;
using UnityEngine.Tilemaps;

/// <summary>
/// Clase base para entidades que se mueven en una cuadrícula.
/// </summary>
public abstract class GridEntity : MonoBehaviour
{
    [Header("Grid Entity Settings")]
    [SerializeField] protected Tilemap tilemap;
    [SerializeField] protected float moveSpeed = 3f;
    [SerializeField] protected CombatController combatController;

    public Vector3Int currentGridPosition { get; private set; }
    public bool isMoving { get; protected set; } = false;

    protected Vector3 targetWorldPosition;

    protected virtual void Awake()
    {
        if (tilemap == null)
        {
            Debug.LogError($"[{name}] Tilemap not assigned and not found by tag 'Tilemap'. Grid movement will not function correctly.", this);
            enabled = false; // Deshabilitar el componente si no hay tilemap
        }
    }

    protected virtual void Start() => SnapToCurrentTransformPosition();

    protected virtual void Update() => HandleGridBasedMovementUpdate();

    protected virtual void HandleGridBasedMovementUpdate()
    {
        if (isMoving && tilemap != null)
        {
            transform.position = Vector3.MoveTowards(transform.position, targetWorldPosition, moveSpeed * Time.deltaTime);
            if (Vector3.Distance(transform.position, targetWorldPosition) < 0.01f)
            {
                transform.position = targetWorldPosition;
                isMoving = false;
                currentGridPosition = tilemap.WorldToCell(targetWorldPosition);
                DestinationReached();
            }
        }
    }

    protected virtual void DestinationReached()
    {
        Debug.LogWarning($"DestinationReached - You should override this method");
    }

    public virtual bool TryMoveToCell(Vector3Int targetCell)
    {
        if (isMoving || tilemap == null)
        {
            return false;
        }

        if (IsValidCellForMovement(targetCell))
        {
            targetWorldPosition = tilemap.GetCellCenterWorld(targetCell);
            isMoving = true;
            return true;
        }
        return false;
    }

    /// <summary>
    /// Set current grid position and targetWorldPosition as current transform.position
    /// </summary>
    /// <param name="cellPosition"></param>
    public void SnapToCell(Vector3Int cellPosition)
    {
        if (tilemap == null) return;
        transform.position = tilemap.GetCellCenterWorld(cellPosition);
        currentGridPosition = cellPosition;
        targetWorldPosition = transform.position; // Sincronizar targetWorldPosition
        isMoving = false;
    }

    public void SnapToCurrentTransformPosition()
    {
        if (tilemap == null)
        {
            Debug.LogError($"tilemap is NULL/EMPTY");
            return;
        }
        currentGridPosition = tilemap.WorldToCell(transform.position);
        SnapToCell(currentGridPosition);
    }

    /// <summary>
    /// Determina si la entidad puede moverse a la celda objetivo.
    /// Las clases derivadas deben sobrescribir esto para su lógica específica.
    /// </summary>
    public virtual bool IsValidCellForMovement(Vector3Int targetCell)
    {
        // Implementación base: solo verifica si hay un tile.
        return tilemap != null && tilemap.HasTile(targetCell);
    }
}