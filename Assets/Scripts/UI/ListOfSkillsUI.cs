using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class ListOfSkillsUI : MonoBehaviour
{
    public GameObject parent;
    public GameObject content;
    public GameObject itemPrefab;
    public Player player;
    public ChipsDataSO chipsDataSO;

    public event Action<ListOfSkillsUI> EventClose;

    private List<SkillDataUI> spawnedItems = new();

    void OnEnable()
    {
        if (spawnedItems.Count == 0)
        {
            foreach (var item in chipsDataSO.skills)
            {
                var clone = Instantiate(itemPrefab, content.transform);
                clone.name = item.skillName;
                var skillDataUI = clone.GetComponent<SkillDataUI>();
                spawnedItems.Add(skillDataUI);
                skillDataUI.Initialize(this, item);
            }
        }
    }

    [ContextMenu(nameof(Show))]
    public void Show() => parent.SetActive(true);

    internal void Selection(SkillDataUI skillDataUI)
    {
        if (player == null)
        {
            Debug.LogError($"player is NULL/EMPTY");
            return;
        }
        if (skillDataUI == null)
        {
            Debug.LogError($"skillDataUI is NULL/EMPTY");
            return;
        }
        if (skillDataUI.SkillDataSO == null)
        {
            Debug.LogError($"skillDataUI.SkillDataSO is NULL/EMPTY");
            return;
        }
        player.AddAvailableSkill(skillDataUI.SkillDataSO);
        EventClose?.Invoke(this);
        parent.SetActive(false);
    }
}