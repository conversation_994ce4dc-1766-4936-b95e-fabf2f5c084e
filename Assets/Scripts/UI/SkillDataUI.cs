using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class SkillDataUI : MonoBehaviour
{
    [SerializeField] private TMP_Text nameText;
    [SerializeField] private TMP_Text amountText;
    [SerializeField] private TMP_Text descriptionText;
    [SerializeField] private Button button;
    [SerializeField] private ListOfSkillsUI listOfSkillsUI;

    [SerializeField] private SkillDataSO skillDataSO;

    public SkillDataSO SkillDataSO { get => skillDataSO; }

    void Awake()
    {
        if (button != null)
        {
            button.onClick.AddListener(OnButtonClick);
        }
    }

    public void Initialize(ListOfSkillsUI listOfSkillsUI, SkillDataSO skillDataSO)
    {
        this.listOfSkillsUI = listOfSkillsUI;
        this.skillDataSO = skillDataSO;
        if (nameText != null)
        {
            nameText.text = $"{skillDataSO.skillTypeLetter} {skillDataSO.skillName}";
        }
        if (amountText != null)
        {
            amountText.text = skillDataSO.amount.ToString();
        }
        if (descriptionText != null)
        {
            descriptionText.text = skillDataSO.description;
        }
    }

    private void OnButtonClick()
    {
        if (listOfSkillsUI == null)
        {
            Debug.LogError($"listOfSkillsUI is NULL/EMPTY");
            return;
        }
        listOfSkillsUI.Selection(this);
    }

    void OnDestroy()
    {
        if (button == null)
        {
            Debug.LogError($"button is NULL/EMPTY");
            return;
        }
        button.onClick.RemoveListener(OnButtonClick);
    }


}
