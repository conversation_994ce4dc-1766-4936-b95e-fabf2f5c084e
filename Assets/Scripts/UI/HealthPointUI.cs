using System;
using TMPro;
using UnityEngine;

public class HealthPointUI : MonoBehaviour
{
    [SerializeField] private Player player;
    [SerializeField] private TMP_Text healthPointText;

    void Awake()
    {
        if (healthPointText == null)
        {
            Debug.LogError($"healthPointText is NULL/EMPTY");
            Destroy(this);
            return;
        }
        player.EventTakingDamage += OnDamage;
        OnDamage(player);
    }

    private void OnDamage(GridUnit unit)
    {
        healthPointText.text = unit.currentHealthPoint.ToString();
    }
}
