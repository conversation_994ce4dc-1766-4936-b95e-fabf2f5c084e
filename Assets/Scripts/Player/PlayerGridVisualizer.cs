using UnityEngine;
using UnityEngine.Tilemaps;

[RequireComponent(typeof(PlayerMovement))]
public class PlayerGridVisualizer : MonoBehaviour
{
    [SerializeField] private Color playerPositionColor = Color.green;
    [SerializeField] private Color validMoveColor = Color.blue;
    [SerializeField] private Color invalidMoveColor = Color.red;
    [SerializeField] private float gizmoSize = 0.3f;

    private PlayerMovement playerMovement;
    private Tilemap tilemap;

    private void Awake()
    {
        playerMovement = GetComponent<PlayerMovement>();
    }

    private void Start()
    {
        // Find the tilemap if available
        if (tilemap == null)
        {
            GameObject tilemapObj = GameObject.FindWithTag("Tilemap");
            if (tilemapObj != null)
            {
                tilemap = tilemapObj.GetComponent<Tilemap>();
            }
        }
    }

    private void OnDrawGizmos()
    {
        if (!Application.isPlaying) return;

        // Draw player position
        Gizmos.color = playerPositionColor;
        Gizmos.DrawSphere(transform.position, gizmoSize);

        // Draw possible move positions
        if (tilemap != null)
        {
            // Get current cell position
            Vector3Int currentCell = tilemap.WorldToCell(transform.position);

            // Draw adjacent cells
            DrawAdjacentCell(currentCell + Vector3Int.up);
            DrawAdjacentCell(currentCell + Vector3Int.down);
            DrawAdjacentCell(currentCell + Vector3Int.left);
            DrawAdjacentCell(currentCell + Vector3Int.right);
        }
    }

    private void DrawAdjacentCell(Vector3Int cellPosition)
    {
        if (tilemap != null)
        {
            Vector3 worldPosition = tilemap.GetCellCenterWorld(cellPosition);
            bool hasTile = tilemap.HasTile(cellPosition);

            // First check if there's a tile
            if (!hasTile)
            {
                Gizmos.color = invalidMoveColor;
                Gizmos.DrawWireCube(worldPosition, Vector3.one * 0.8f);
                return;
            }

            // Check if the tile belongs to the player
            bool isPlayerOwned = false;
            TileOwnershipManager ownershipManager = tilemap.GetComponent<TileOwnershipManager>();

            if (ownershipManager != null)
            {
                OwnershipTileSO.Owner owner = ownershipManager.GetTileOwner(cellPosition);
                isPlayerOwned = (owner == OwnershipTileSO.Owner.Player);
            }

            Gizmos.color = isPlayerOwned ? validMoveColor : invalidMoveColor;
            Gizmos.DrawWireCube(worldPosition, Vector3.one * 0.8f);
        }
    }
}
