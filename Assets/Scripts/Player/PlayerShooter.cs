using UnityEngine;

public class PlayerShooter : MonoBehaviour
{
    [Header("Shooting Settings")]
    [SerializeField] private KeyCode shootKey = KeyCode.K;
    [SerializeField] private GameObject projectilePrefab;
    [SerializeField] private Transform firePoint;
    [SerializeField] private float cooldownTime = 0.5f;
    [SerializeField] private float projectileSpeed = 10f;

    [Header("Visual Feedback")]
    [SerializeField] private bool showDebugInfo = true;
    [SerializeField] private Color cooldownColor = Color.red;
    [SerializeField] private Color readyColor = Color.green;

    // Private variables
    private bool canShoot = true;
    private float cooldownTimer = 0f;
    private SpriteRenderer spriteRenderer;
    private Color originalColor;

    private void Awake()
    {
        // Get components
        spriteRenderer = GetComponent<SpriteRenderer>();

        // Store original color if we have a sprite renderer
        if (spriteRenderer != null)
        {
            originalColor = spriteRenderer.color;
        }

        // Create a fire point if none is assigned
        if (firePoint == null)
        {
            GameObject newFirePoint = new GameObject("FirePoint");
            newFirePoint.transform.parent = transform;
            newFirePoint.transform.localPosition = new Vector3(0.5f, 0f, 0f); // Default position in front of player
            firePoint = newFirePoint.transform;
        }
    }

    private void Update()
    {
        // Handle cooldown timer
        if (!canShoot)
        {
            cooldownTimer -= Time.deltaTime;

            if (cooldownTimer <= 0f)
            {
                canShoot = true;

                // Reset color if we're using visual feedback
                if (spriteRenderer != null && showDebugInfo)
                {
                    spriteRenderer.color = originalColor;
                }
            }
        }

        // Check for input
        if (Input.GetKey(shootKey))
        {
            TryShoot();
        }
    }

    private void TryShoot()
    {
        if (canShoot)
        {
            Shoot();
            StartCooldown();
        }
    }

    private void Shoot()
    {
        // Check if we have a projectile prefab
        if (projectilePrefab == null)
        {
            Debug.LogWarning("No projectile prefab assigned to PlayerShooter!");
            return;
        }

        // Instantiate the projectile at the fire point
        GameObject projectile = Instantiate(projectilePrefab, firePoint.position, firePoint.rotation);

        // Get the direction based on player's facing direction
        Vector2 direction = GetShootingDirection();

        // Add velocity to the projectile
        Rigidbody2D rb = projectile.GetComponent<Rigidbody2D>();
        if (rb != null)
        {
            rb.linearVelocity = direction * projectileSpeed;
        }
        else
        {
            // If no rigidbody, add one
            rb = projectile.AddComponent<Rigidbody2D>();
            rb.gravityScale = 0f; // No gravity for projectiles
            rb.linearVelocity = direction * projectileSpeed;
        }

        // Set up the projectile (if it has a Projectile component)
        Projectile projectileComponent = projectile.GetComponent<Projectile>();
        if (projectileComponent != null)
        {
            projectileComponent.Initialize(gameObject, direction);
        }

    }

    private Vector2 GetShootingDirection()
    {
        // Default direction (right)
        Vector2 direction = Vector2.right;

        // Check if the player has a sprite renderer to determine facing
        if (spriteRenderer != null && spriteRenderer.flipX)
        {
            direction = Vector2.left;
        }

        return direction;
    }

    private void StartCooldown()
    {
        canShoot = false;
        cooldownTimer = cooldownTime;

        // Visual feedback during cooldown
        if (spriteRenderer != null && showDebugInfo)
        {
            spriteRenderer.color = cooldownColor;
        }
    }

    // Draw the fire point in the editor
    private void OnDrawGizmos()
    {
        if (firePoint != null && showDebugInfo)
        {
            Gizmos.color = canShoot ? readyColor : cooldownColor;
            Gizmos.DrawSphere(firePoint.position, 0.1f);
            Gizmos.DrawRay(firePoint.position, GetShootingDirection() * 1f);
        }
    }
}
