using UnityEngine;

public class PlayerMovement : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private TileOwnershipManager ownershipManager;
    [SerializeField] private Player player;

    // Components
    [SerializeField] private Sprite<PERSON><PERSON><PERSON> spriteRenderer;

    // Input Movement variables
    private Vector2 movement;

    protected void Update()
    {
        // Get input
        float horizontalInput = Input.GetAxisRaw("Horizontal");
        float verticalInput = Input.GetAxisRaw("Vertical");

        // Create movement vector
        movement = new Vector2(horizontalInput, verticalInput);

        // Normalize for consistent speed in all directions
        if (movement.magnitude > 1f)
        {
            movement.Normalize();
        }

        HandlePlayerGridMovementInput();

        // Flip sprite based on movement direction
        // if (horizontalInput != 0 && spriteRenderer != null)
        // {
        //     spriteRenderer.flipX = horizontalInput < 0;
        // }
    }

    private void HandlePlayerGridMovementInput()
    {
        // Only process new movement if we're not already moving
        if (!player.isMoving) // Usar base.isMoving de GridEntity
        {
            if (movement.magnitude > 0.1f)
            {
                Vector3Int moveDirection = Vector3Int.zero;
                // Determine which direction has the largest input
                if (Mathf.Abs(movement.x) > Mathf.Abs(movement.y))
                {
                    // Horizontal movement
                    moveDirection.x = (int)Mathf.Sign(movement.x);
                }
                else
                {
                    // Vertical movement
                    moveDirection.y = (int)Mathf.Sign(movement.y);
                }

                var targetGridPosition = player.currentGridPosition + moveDirection;
                if (IsValidCellForMovement(targetGridPosition))
                {
                    player.TryMoveToCell(targetGridPosition);
                }
            }
        }
    }

    private bool IsValidCellForMovement(Vector3Int targetCell)
    {
        if (!player.IsValidCellForMovement(targetCell))
        {
            return false;
        }

        if (ownershipManager != null)
        {
            OwnershipTileSO.Owner owner = ownershipManager.GetTileOwner(targetCell);
            return owner == OwnershipTileSO.Owner.Player;
        }

        return true;
    }
    
}
