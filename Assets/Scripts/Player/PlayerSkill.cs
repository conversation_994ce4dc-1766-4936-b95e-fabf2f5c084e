using UnityEngine;

public class PlayerSkill : MonoBehaviour
{
    [Head<PERSON>("Skills Settings")]
    [SerializeField] private KeyCode skillKey = KeyCode.J;
    [SerializeField] private Player player;
    [SerializeField] private SpriteRenderer spriteRenderer;
    [SerializeField] private float cooldownTime = 0.5f;

    private float cooldownPending = 0f;

    private void Update()
    {
        // Handle cooldown timer
        cooldownPending -= Time.deltaTime;

        if (cooldownPending > 0)
        {
            // // Reset color if we're using visual feedback
            // if (spriteRenderer != null && showDebugInfo)
            // {
            //     spriteRenderer.color = originalColor;
            // }
            return;
        }

        // Check for input
        if (Input.GetKeyDown(skillKey))
        {
            if (player.UseSkill() == null)
            {
                return;
            }
            cooldownPending = cooldownTime;
        }
    }
}
