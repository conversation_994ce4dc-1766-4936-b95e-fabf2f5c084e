using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class GridUnit : GridEntity, IDamageable
{
    /// <summary>
    /// When Unit takes damage
    /// </summary>
    public event Action<GridUnit> EventTakingDamage;
    /// <summary>
    /// When Unit dies
    /// </summary>
    public event Action<GridUnit> EventDied;
    /// <summary>
    /// When Unit uses a skill
    /// </summary>
    public event Action<GridUnit> EventUseSkill;

    public int currentHealthPoint;
    public int maximumHealthPoint;
    [SerializeField] protected List<SkillDataSO> availableSkills;
    [SerializeField] protected SpriteRenderer spriteRenderer;

    public bool CanMove { get; protected set; } = true;
    public bool CanAction { get; protected set; } = true;

    protected HashSet<SkillBehaviour> skillBehavioursSet = new();

    private Color originalColor;

    protected override void Start()
    {
        base.Start();
        originalColor = spriteRenderer.color;
    }

    protected override void Awake()
    {
        base.Awake();
        if (availableSkills.Count >= 2)
        {
            availableSkills.Reverse();
        }
    }

    public void TakeDamage(int damage)
    {
        Debug.Log($"'{name}' takes damage {damage}");
        currentHealthPoint = Math.Clamp(currentHealthPoint - damage, 0, maximumHealthPoint);
        if (currentHealthPoint <= 0)
        {
            currentHealthPoint = 0;
            Die();
        }
        FlashOnDamage();
        EventTakingDamage?.Invoke(this);
    }

    protected void FlashOnDamage()
    {
        // Cancel any previous tween
        spriteRenderer.DOKill();

        Sequence seq = DOTween.Sequence();
        seq.Append(spriteRenderer.DOColor(Color.red, 0.1f)) // Change red rapidly
            .AppendInterval(0.2f) // Wait a little bit
            .Append(spriteRenderer.DOColor(originalColor, 0.3f)) // Back to original color
            .SetEase(Ease.Linear)
            .SetLink(gameObject);
    }

    #region Blockers
    public virtual void BlockMovement(float secondsToRelease)
    {
        CancelInvoke(nameof(UnblockMovement));
        CanMove = false;
        Invoke(nameof(UnblockMovement), secondsToRelease);
    }
    public virtual void UnblockMovement() => CanMove = true;
    public virtual void BlockAction(float secondsToRelease)
    {
        CancelInvoke(nameof(UnblockAction));
        CanAction = false;
        Invoke(nameof(UnblockAction), secondsToRelease);
    }
    public virtual void UnblockAction() => CanAction = true;
    /// <summary>
    /// Block movement and action for the sepecific seconds
    /// </summary>
    /// <param name="secondsToRelease"></param>
    public void BlockMovementAndAction(float secondsToRelease)
    {
        BlockMovement(secondsToRelease);
        BlockAction(secondsToRelease);
    }
    public void UnblockMovementAndAction()
    {
        UnblockMovement();
        UnblockAction();
    }
    #endregion

    protected override void DestinationReached() => combatController.UpdateGridUnit(this, currentGridPosition);

    /// <summary>
    /// Llama esta función para usar una habilidad por índice.
    /// </summary>
    public virtual SkillBehaviour UseSkill(int skillIndex = -1)
    {
        if (availableSkills == null || availableSkills.Count == 0)
        {
            Debug.LogError($"{name} availableSkills is NULL");
            return null;
        }
        if (availableSkills == null || availableSkills.Count == 0)
        {
            Debug.LogWarning($"{name} availableSkills is empty");
            return null;
        }
        if (isMoving)
        {
            Debug.LogWarning($"{name} is moving, can't use skill");
            return null;
        }
        if (skillIndex < 0 || skillIndex >= availableSkills.Count)
        {
            skillIndex = availableSkills.Count - 1;
        }

        var skillData = availableSkills[skillIndex];
        var prefab = skillData.visualPrefab;
        var clone = Instantiate(prefab, tilemap.GetCellCenterWorld(currentGridPosition), Quaternion.identity);

        var skillBehaviour = clone.GetComponent<SkillBehaviour>();
        if (skillBehaviour == null)
        {
            Debug.LogError("El prefab de la habilidad no tiene un SkillBehaviour.", clone);
            Destroy(clone);
            return null;
        }

        skillBehavioursSet.Add(skillBehaviour);
        skillBehaviour.Initialize(skillData);
        skillBehaviour.EventFinish += OnSkillFinish;
        skillBehaviour.Activate(currentGridPosition, tilemap, this, combatController);
        EventUseSkill?.Invoke(this);
        return skillBehaviour;
    }

    protected virtual void OnSkillFinish(SkillBehaviour skill)
    {
        skill.EventFinish -= OnSkillFinish;
        skillBehavioursSet.Remove(skill);
    }

    // Clears all pending skill behaviours set and unsubscribe.
    protected virtual void OnDestroy() => ClearSkillCasted();

    protected void ClearSkillCasted()
    {
        foreach (var skill in skillBehavioursSet)
        {
            if (skill == null)
            {
                continue;
            }
            skill.EventFinish -= OnSkillFinish;
        }
        skillBehavioursSet.Clear();
    }

    virtual protected void Die()
    {
        ClearSkillCasted();
        EventDied?.Invoke(this);
        combatController.RemoveGridUnit(this);
        Debug.Log($"'{name}' died");
    }

    #region Unity
    [ContextMenu(nameof(Unity_SnapToTransformPosition))]
    public void Unity_SnapToTransformPosition() => SnapToCurrentTransformPosition();
    #endregion

}
