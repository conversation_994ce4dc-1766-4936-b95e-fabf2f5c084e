using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Tilemaps;

public class Player : GridUnit
{
    public float secondsToBlockMovement = 1f;

    [SerializeField] private PlayerMovement playerMovement;
    [SerializeField] private ChipsDataSO chips;
    [SerializeField] private SelectedSkillsController selectedSkillsController;

    public Tilemap Tilemap => tilemap;

    override protected void Awake()
    {
        base.Awake();
        EventTakingDamage += OnTakingDamage;
        var currentGridPosition = tilemap.WorldToCell(transform.position);
        SnapToCurrentTransformPosition();
        combatController.AddGridUnit(currentGridPosition, this);
        if (!playerMovement.enabled)
        {
            playerMovement.enabled = true;
        }
    }

    protected override void Start()
    {
        base.Start();
        selectedSkillsController.Show(availableSkills);
    }

    private void OnTakingDamage(GridUnit unit)
    {
        if (isMoving)
        {
            isMoving = false;
            SnapToCell(currentGridPosition);
            // SnapToCurrentTransformPosition();
        }
        BlockMovement(secondsToBlockMovement);
    }

    /// <summary>
    /// Block movement and unblocks after x seconds
    /// </summary>
    /// <param name="secondsToUnblock"></param>
    public override void BlockMovement(float secondsToUnblock)
    {
        base.BlockMovement(secondsToUnblock);
        playerMovement.enabled = false;
    }

    public override void UnblockMovement()
    {
        base.UnblockMovement();
        playerMovement.enabled = true;
    }

    override public SkillBehaviour UseSkill(int skillIndex = -1)
    {
        var skill = base.UseSkill(skillIndex);
        if (skill == null)
        {
            return null;
        }
        availableSkills.Remove(skill.skillData);
        return skill;
    }

    protected override void Die()
    {
        base.Die();
        Destroy(gameObject);
    }

    override protected void OnDestroy()
    {
        CancelInvoke();
        base.OnDestroy();
        EventTakingDamage -= OnTakingDamage;
    }

    internal void AddAvailableSkill(SkillDataSO skillDataSO)
    {
        availableSkills.Clear();
        availableSkills.Add(skillDataSO);
    }
}
