using System;
using System.Collections.Generic;
using System.Text;
using UnityEditor;
using UnityEngine;

/// <summary>
/// Tracks what grid position each thing is currently using and part of the game too
/// </summary>
public class CombatController : MonoBehaviour
{
    public class CombatControllerHelper
    {
        private CombatController combatController;

        public CombatControllerHelper(CombatController combatController)
        {
            this.combatController = combatController;
        }

        public Vector3Int GetRandomPlayerPositionInGrid()
        {
            var randomX = UnityEngine.Random.Range(combatController.topLeft.x, combatController.enemyTopLeft.x);
            // The -1 is for including the last row
            var randomY = UnityEngine.Random.Range(combatController.topLeft.y, combatController.bottomRight.y - 1);
            return new Vector3Int(randomX, randomY, combatController.topLeft.z);
        }
    }

    private Tracker<GridUnit> gridUnitTracker = new();
    private Tracker<SkillBehaviour> skillTracker = new();

    public event Action<GridUnit, Vector3Int> EventGridUnitEnter;
    public event Action<SkillBehaviour, Vector3Int> EventSkillEnter;

    public readonly Vector3Int topLeft = new(-4, 1, 0);
    public readonly Vector3Int bottomRight = new(3, -2, 0);

    public readonly Vector3Int enemyTopLeft = new(0, 1, 0);
    public readonly Vector3Int enemyBottomRight = new(0, 1, 0);

    public CombatControllerHelper Helper { get; private set; }

    void Awake()
    {
        Helper = new CombatControllerHelper(this);
    }

    #region Skills
    public void AddSkill(SkillBehaviour skillBehaviour, Vector3Int gridPosition) => AddSkill(gridPosition, skillBehaviour);
    public void AddSkill(Vector3Int gridPosition, SkillBehaviour skillBehaviour)
    {
        if (!skillTracker.Add(gridPosition, skillBehaviour))
        {
            return;
        }
        EventSkillEnter?.Invoke(skillBehaviour, gridPosition);
    }
    public void RemoveSkill(Vector3Int gridPosition) => skillTracker.Remove(gridPosition);
    public void RemoveSkill(SkillBehaviour skillBehaviour) => skillTracker.Remove(skillBehaviour);
    public void UpdateSkill(SkillBehaviour skillBehaviour, Vector3Int newGridPosition)
    {
        if (!skillTracker.Update(skillBehaviour, newGridPosition))
        {
            return;
        }
        EventSkillEnter?.Invoke(skillBehaviour, newGridPosition);
    }
    #endregion

    #region GridUnit
    public void AddGridUnit(GridUnit gridUnit, Vector3Int gridPosition) => AddGridUnit(gridPosition, gridUnit);
    public void AddGridUnit(Vector3Int gridPosition, GridUnit gridUnit)
    {
        if (!gridUnitTracker.Add(gridPosition, gridUnit))
        {
            return;
        }
        EventGridUnitEnter?.Invoke(gridUnit, gridPosition);
    }
    public void RemoveGridUnit(GridUnit gridUnit) => gridUnitTracker.Remove(gridUnit);
    public void UpdateGridUnit(GridUnit gridUnit, Vector3Int newGridPosition)
    {
        if (!gridUnitTracker.Update(gridUnit, newGridPosition))
        {
            Debug.LogError($"Unit {gridUnit} is not set");
            return;
        }
        EventGridUnitEnter?.Invoke(gridUnit, newGridPosition);
    }
    public bool TryGetGridUnitFromPosition(Vector3Int gridPosition, out GridUnit gridUnit) => gridUnitTracker.TryGetFromPosition(gridPosition, out gridUnit);

    public List<GridUnit> GetGridUnitFoes(string tag)
    {
        var foes = new List<GridUnit>();
        foreach (var gridUnit in gridUnitTracker.Values)
        {
            if (!gridUnit.CompareTag(tag))
            {
                foes.Add(gridUnit);
            }
        }
        return foes;
    }
    #endregion

    #region Unity
#if UNITY_EDITOR
    [ContextMenu(nameof(PrintValues))]
    public void PrintValues()
    {
        var sb = new StringBuilder();
        foreach (var kv in gridUnitTracker.Dictionary)
        {
            sb.Append($"{kv.Key.name} {kv.Value} - ");
        }
        print(sb.ToString());
    }
#endif
    #endregion
}
