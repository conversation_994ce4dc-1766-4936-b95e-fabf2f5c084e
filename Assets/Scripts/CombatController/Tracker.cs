using System.Collections.Generic;
using UnityEngine;

internal class Tracker<T> where T : MonoBehaviour
{
    private Dictionary<Vector3Int, T> gridPosition2Item = new();
    private Dictionary<T, Vector3Int> item2GridPosition = new();

    public IEnumerable<T> Values { get => gridPosition2Item.Values; }
    public Dictionary<T, Vector3Int> Dictionary { get => item2GridPosition; }

    public bool Add(Vector3Int gridPosition, T item)
    {
        if (item == null)
        {
            Debug.LogError($"item is NULL/EMPTY");
            return false;
        }
        if (gridPosition2Item.ContainsKey(gridPosition))
        {
            Debug.LogError($"already contains key gridPosition {gridPosition} when adding item {item}");
            return false;
        }
        if (item2GridPosition.ContainsKey(item))
        {
            // Debug.LogError($"already contains key gridPosition {gridPosition} when adding item {item}");
        }
        gridPosition2Item.Add(gridPosition, item);
        item2GridPosition.Add(item, gridPosition);
        return true;
    }

    public void Remove(T item)
    {
        if (!item2GridPosition.TryGetValue(item, out var gridPosition))
        {
            // Debug.LogError($"item {item} not found");
            return;
        }
        item2GridPosition.Remove(item);
        gridPosition2Item.Remove(gridPosition);
    }
    public void Remove(Vector3Int gridPosition)
    {
        if (!gridPosition2Item.TryGetValue(gridPosition, out var gridUnit))
        {
            Debug.LogError($"gridPosition {gridPosition} not found");
            return;
        }
        gridPosition2Item.Remove(gridPosition);
        item2GridPosition.Remove(gridUnit);
    }

    public bool Update(T item, Vector3Int newGridPosition)
    {
        if (!item2GridPosition.ContainsKey(item))
        {
            Debug.LogError($"does not contain key {item}");
            return false;
        }
        var currentGridPosition = item2GridPosition[item];
        if (gridPosition2Item.TryGetValue(newGridPosition, out var gridUnitUsingThePosition) && item != gridUnitUsingThePosition)
        {
            Debug.Log($"{item.name} is trying to use {newGridPosition} but is being used by {gridUnitUsingThePosition.name}. Ignoring this request");
            // There is someone else in this position in this cell. Dont' remove it.
            return false;
        }
        if (currentGridPosition == newGridPosition)
        {
            // Same position, skipping
            return true;
        }

        // Position is empty.
        gridPosition2Item.Remove(currentGridPosition);
        item2GridPosition.Remove(item);
        gridPosition2Item.Add(newGridPosition, item);
        item2GridPosition.Add(item, newGridPosition);
        return true;
        // Debug.Log($"{gridUnit.name} moved from {currentGridPosition} to {nextGridPosition}");
    }

    public bool TryGetFromPosition(Vector3Int gridPosition, out T item) => gridPosition2Item.TryGetValue(gridPosition, out item);

}
