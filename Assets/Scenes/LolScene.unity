%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &161743854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 161743856}
  - component: {fileID: 161743855}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!212 &161743855
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 161743854}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 43a7bb429f3804d0ca4d46e30db4a54a, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &161743856
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 161743854}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 574171483}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &184583817
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 184583818}
  - component: {fileID: 184583819}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &184583818
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 184583817}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: **********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &184583819
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 184583817}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 43a7bb429f3804d0ca4d46e30db4a54a, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1001 &196792566
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_Name
      value: DislikeEnemy
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 9002661557395101491, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: moveSpeed
      value: 0.9
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 66cc7425bca5f45edbf41a27c77edb68, type: 3}
--- !u!1 &284434046
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 284434047}
  - component: {fileID: 284434049}
  - component: {fileID: 284434048}
  m_Layer: 6
  m_Name: Tilemap Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &284434047
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 284434046}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1458798063}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!483693784 &284434048
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 284434046}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 0
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &284434049
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 284434046}
  m_Enabled: 1
  m_Tiles:
  - first: {x: -4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -4, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -4, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -4, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 16
    m_Data: {fileID: 11400000, guid: 7b5c30e65830f46afb112e6d88bc1f99, type: 2}
  - m_RefCount: 16
    m_Data: {fileID: 11400000, guid: f0e1c572d0cc54c4f807655f8505528f, type: 2}
  m_TileSpriteArray:
  - m_RefCount: 32
    m_Data: {fileID: 7482667652216324306, guid: a65a294a0ca5b4330898726b93fa6e40, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 32
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 16
    m_Data: {r: 0.9578586, g: 0.9782206, b: 0.990566, a: 1}
  - m_RefCount: 16
    m_Data: {r: 1, g: 0.3, b: 0.3, a: 1}
  - m_RefCount: 0
    m_Data: {r: 0.95686275, g: 0.9764706, b: 0.99215686, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: -6, y: -3, z: 0}
  m_Size: {x: 12, y: 7, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!114 &287421531 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
  m_PrefabInstance: {fileID: 643587747525445282}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 89d866d87667d42c09f1430c408e96e1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &391281145
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 839941890697044924, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: ownershipManager
      value: 
      objectReference: {fileID: 422035517}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -3.45
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.28
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3458172625772760724, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_Name
      value: Player (1)
      objectReference: {fileID: 0}
    - target: {fileID: 3458172625772760724, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: moveSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: availableSkills.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[0]'
      value: 
      objectReference: {fileID: 11400000, guid: e3ea78ca79a3d453d9158d1e08b00eeb, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[1]'
      value: 
      objectReference: {fileID: 11400000, guid: 642177c2e7bac41268835f700114c542, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[2]'
      value: 
      objectReference: {fileID: 11400000, guid: 292cbce9167804f5e95f7905aeb7d442, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
--- !u!1 &422035514
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 422035516}
  - component: {fileID: 422035517}
  m_Layer: 0
  m_Name: TileOwnershipManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &422035516
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 422035514}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &422035517
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 422035514}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e0468b04fdc8440f8284eeeb3ba2656, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  tilemap: {fileID: 284434049}
--- !u!1 &442068746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 442068748}
  - component: {fileID: 442068747}
  m_Layer: 0
  m_Name: SelectedSkillsController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &442068747
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 442068746}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b45feefa0328949288acc6d74fc0f560, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  rootGameObject: {fileID: 694641989}
  itemTemplate1st: {fileID: 574171484}
  itemTemplateOthers: {fileID: **********}
  offsetBetweenItems: 0.7
  gridUnitToSubscribe: {fileID: 287421531}
  debug_skillDataSO:
  - {fileID: 11400000, guid: 5e0faa5a730d343f780a94c6ffcee02e, type: 2}
  - {fileID: 11400000, guid: 841bedbe8089548a1b87dd3ac60ecf05, type: 2}
  - {fileID: 11400000, guid: 51ce0447a501e4b7a8a99a068c93d066, type: 2}
  - {fileID: 11400000, guid: 292cbce9167804f5e95f7905aeb7d442, type: 2}
  - {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
  - {fileID: 11400000, guid: 063649a2654a04f92a1e882fcb8d02c5, type: 2}
  - {fileID: 11400000, guid: b7016c881c68a4543aa279066def01a3, type: 2}
  - {fileID: 11400000, guid: 474cac84758424654adf01f735d532fc, type: 2}
--- !u!4 &442068748
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 442068746}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -3.41, y: -2.64, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 574171483}
  - {fileID: **********}
  - {fileID: 694641990}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &519420028
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 519420032}
  - component: {fileID: 519420031}
  - component: {fileID: 519420029}
  - component: {fileID: 519420030}
  - component: {fileID: 519420033}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &519420029
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  m_Enabled: 1
--- !u!114 &519420030
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!20 &519420031
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 10
  field of view: 34
  orthographic: 1
  orthographic size: 3.63
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 0
  m_HDR: 1
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 0
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &519420032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &519420033
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d213138111f4840d2936e7f5f9bf1bb1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &531041372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 531041375}
  - component: {fileID: 531041374}
  - component: {fileID: 531041373}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &531041373
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 531041372}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: '-'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 8.5
  m_fontSizeBase: 8.5
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 531041374}
  m_maskType: 0
--- !u!23 &531041374
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 531041372}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!224 &531041375
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 531041372}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: **********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 1, y: 1}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &574171482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 574171483}
  - component: {fileID: 574171484}
  m_Layer: 0
  m_Name: ItemTemplate1st
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &574171483
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 574171482}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 161743856}
  m_Father: {fileID: 442068748}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &574171484
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 574171482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5fac3411707524583b50f9cd4aa98cdf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemTemplate: {fileID: 161743855}
  textTemplate: {fileID: 0}
--- !u!1 &588108308
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 588108310}
  - component: {fileID: 588108309}
  m_Layer: 0
  m_Name: CombatController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &588108309
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 588108308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 708b51affadc648ef8e641af5e0c360b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &588108310
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 588108308}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &618560519
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_Name
      value: Enemy Avion
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 9002661557395101491, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 4041784615371743649, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 0}
  m_SourcePrefab: {fileID: 100100000, guid: f5aac47d0a3d24fdaa27d5ed94f78b4c, type: 3}
--- !u!1 &619394800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 619394802}
  - component: {fileID: 619394801}
  m_Layer: 0
  m_Name: Global Light 2D
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &619394801
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 619394800}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 2
  m_LightType: 4
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.5
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 00000000
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 3
  m_NormalMapQuality: 2
  m_UseNormalMap: 0
  m_ShadowsEnabled: 0
  m_ShadowIntensity: 0.75
  m_ShadowSoftness: 0
  m_ShadowSoftnessFalloffIntensity: 0.5
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 0
  m_PointLightOuterRadius: 1
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!4 &619394802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 619394800}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.04, y: -0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &621050858
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1243450177}
    m_Modifications:
    - target: {fileID: 3460815052398111206, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3460815052398111206, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3460815052398111206, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_SizeDelta.x
      value: -17
      objectReference: {fileID: 0}
    - target: {fileID: 3882005290601081366, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_Color.a
      value: 0.5686275
      objectReference: {fileID: 0}
    - target: {fileID: 5760457941586497367, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_Name
      value: ListOfSkills
      objectReference: {fileID: 0}
    - target: {fileID: 6257695954934512902, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6257695954934512902, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_SizeDelta.x
      value: -17
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6624820580034918072, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6624820580034918072, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8798425389760562710, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8798425389760562710, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8825236604030988072, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8921272927947521967, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: player
      value: 
      objectReference: {fileID: 287421531}
    - target: {fileID: 9212363467730853935, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
      propertyPath: m_Value
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
--- !u!1 &694641989
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 694641990}
  m_Layer: 0
  m_Name: RootGameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &694641990
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 694641989}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 442068748}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &793913968
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_Name
      value: Enemy Virus
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 9002661557395101491, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 783d53516c79a46a08fee1e4e6a9a1b3, type: 3}
--- !u!1 &963138994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 963138995}
  - component: {fileID: 963138997}
  - component: {fileID: 963138996}
  m_Layer: 5
  m_Name: Text (TMP) Label HP
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &963138995
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 963138994}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2031392355}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 60, y: 50}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &963138996
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 963138994}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: HP
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 35.6
  m_fontSizeBase: 35.6
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &963138997
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 963138994}
  m_CullTransparentMesh: 1
--- !u!1001 &1227174729
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_Name
      value: Enemy Clock
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 9002661557395101491, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e041a4cab3d1d42adb370e321fd6b963, type: 3}
--- !u!1001 &1231149129
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1706565854333908703, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_Name
      value: CannonEnemy
      objectReference: {fileID: 0}
    - target: {fileID: 1706565854333908703, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2628960156748613024, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 2628960156748613024, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5a3c2813b448b470e8614396417a2721, type: 3}
--- !u!1001 &1243022527
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: m_Name
      value: Enemy Prism
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 9002661557395101491, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 4041784615371743649, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
      insertIndex: -1
      addedObject: {fileID: 0}
  m_SourcePrefab: {fileID: 100100000, guid: 5d92d30ce0afd444a8ef1f2ad9529cca, type: 3}
--- !u!1 &1243450173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1243450177}
  - component: {fileID: 1243450176}
  - component: {fileID: 1243450175}
  - component: {fileID: 1243450174}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!114 &1243450174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1243450173}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1243450175
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1243450173}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0.5
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1243450176
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1243450173}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1243450177
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1243450173}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2031392355}
  - {fileID: 1771759594}
  - {fileID: 1925685031}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &1267324713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1267324714}
  - component: {fileID: 1267324716}
  - component: {fileID: 1267324715}
  m_Layer: 6
  m_Name: Tilemap Effects
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1267324714
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1267324713}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1458798063}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!483693784 &1267324715
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1267324713}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 0
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &1267324716
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1267324713}
  m_Enabled: 1
  m_Tiles:
  - first: {x: -4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -4, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -4, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -4, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: -1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 16
    m_Data: {fileID: 11400000, guid: 9eabb7b930b974b69bdc9a8efe2c12f2, type: 2}
  - m_RefCount: 16
    m_Data: {fileID: 11400000, guid: 7e7bcf18f43d44069a700054a2bcc3cb, type: 2}
  m_TileSpriteArray:
  - m_RefCount: 32
    m_Data: {fileID: 21300000, guid: 317c66d8d8ec74f03b2d0bd7f6699ae8, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 32
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 0
    m_Data: {r: 0.9578586, g: 0.9782206, b: 0.990566, a: 1}
  - m_RefCount: 16
    m_Data: {r: 1, g: 1, b: 1, a: 1}
  - m_RefCount: 16
    m_Data: {r: 0, g: 0, b: 0, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: -6, y: -3, z: 0}
  m_Size: {x: 12, y: 7, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!1 &**********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1394732087}
  - component: {fileID: **********}
  m_Layer: 5
  m_Name: HealhPointUI
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1394732087
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1771759594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5883a34590c0c446c8f200e320893b51, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  player: {fileID: 287421531}
  healthPointText: {fileID: **********}
--- !u!1 &**********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: **********}
  - component: {fileID: **********}
  m_Layer: 0
  m_Name: ItemTemplateOthers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &**********
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 531041375}
  - {fileID: 184583818}
  m_Father: {fileID: 442068748}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5fac3411707524583b50f9cd4aa98cdf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemTemplate: {fileID: 184583819}
  textTemplate: {fileID: 531041373}
--- !u!1 &1458798061
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1458798063}
  - component: {fileID: 1458798062}
  m_Layer: 0
  m_Name: Grid
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!156049354 &1458798062
Grid:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1458798061}
  m_Enabled: 1
  m_CellSize: {x: 1, y: 1, z: 0}
  m_CellGap: {x: 0, y: 0, z: 0}
  m_CellLayout: 0
  m_CellSwizzle: 0
--- !u!4 &1458798063
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1458798061}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 284434047}
  - {fileID: 1267324714}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1536397632
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1706565854333908703, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_Name
      value: ExplodeEnemy
      objectReference: {fileID: 0}
    - target: {fileID: 1706565854333908703, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2628960156748613024, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 2628960156748613024, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: moveSpeed
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2628960156748613024, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.5
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 262645f5559df4498ac461f04770aec3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 262645f5559df4498ac461f04770aec3, type: 3}
--- !u!1001 &1538425632
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_Name
      value: VacuumEnemy
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 9002661557395101491, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3c943e1e0ec874bb891a63bf09e23f61, type: 3}
--- !u!1 &1583188461
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1583188462}
  - component: {fileID: 1583188464}
  - component: {fileID: **********}
  m_Layer: 5
  m_Name: Text (TMP) HP
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1583188462
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583188461}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2031392355}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 60.5, y: 0}
  m_SizeDelta: {x: 90, y: 50}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583188461}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 9999
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 35.6
  m_fontSizeBase: 35.6
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1583188464
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583188461}
  m_CullTransparentMesh: 1
--- !u!1001 &1747802813
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1708231376372860111, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_Name
      value: MouseEnemy
      objectReference: {fileID: 0}
    - target: {fileID: 5709123083102823500, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9002661557395101491, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 9002661557395101491, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0da1f224d868d44f3a2ba7711f09b1ea, type: 3}
--- !u!1 &1771759593
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1771759594}
  m_Layer: 5
  m_Name: Controllers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!224 &1771759594
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1771759593}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1394732087}
  m_Father: {fileID: 1243450177}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1889078189
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1889078192}
  - component: {fileID: 1889078191}
  - component: {fileID: 1889078190}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!114 &1889078190
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1889078189}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01614664b831546d2ae94a42149d80ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_MoveRepeatDelay: 0.5
  m_MoveRepeatRate: 0.1
  m_XRTrackingOrigin: {fileID: 0}
  m_ActionsAsset: {fileID: -944628639613478452, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_PointAction: {fileID: -1654692200621890270, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MoveAction: {fileID: -8784545083839296357, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_SubmitAction: {fileID: 392368643174621059, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_CancelAction: {fileID: 7727032971491509709, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_LeftClickAction: {fileID: 3001919216989983466, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MiddleClickAction: {fileID: -2185481485913320682, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_RightClickAction: {fileID: -4090225696740746782, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_ScrollWheelAction: {fileID: 6240969308177333660, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDevicePositionAction: {fileID: 6564999863303420839, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDeviceOrientationAction: {fileID: 7970375526676320489, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_DeselectOnBackgroundClick: 1
  m_PointerBehavior: 0
  m_CursorLockBehavior: 0
  m_ScrollDeltaPerTick: 6
--- !u!114 &1889078191
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1889078189}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1889078192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1889078189}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1896381811
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1091050828335338523, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.2804481
      objectReference: {fileID: 0}
    - target: {fileID: 1091050828335338523, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.2804481
      objectReference: {fileID: 0}
    - target: {fileID: 1091050828335338523, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.2804481
      objectReference: {fileID: 0}
    - target: {fileID: 1706565854333908703, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_Name
      value: Enemy Bombardero
      objectReference: {fileID: 0}
    - target: {fileID: 1706565854333908703, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2628960156748613024, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 2628960156748613024, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalPosition.x
      value: 3.44
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.33
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 6954503516059513650, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
      insertIndex: -1
      addedObject: {fileID: 0}
  m_SourcePrefab: {fileID: 100100000, guid: 966952b107101416aa48dd6e8a54d985, type: 3}
--- !u!224 &1925685031 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 6391898696790164179, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
  m_PrefabInstance: {fileID: 621050858}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1925685032 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 8921272927947521967, guid: 3ba8650fb95594b7eaab1b18763ee2bf, type: 3}
  m_PrefabInstance: {fileID: 621050858}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa103114207ed4dde8f0967f7c32fad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2031392354
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2031392355}
  m_Layer: 5
  m_Name: HPSection
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2031392355
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2031392354}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 963138995}
  - {fileID: 1583188462}
  m_Father: {fileID: 1243450177}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 157.6, y: 100}
  m_Pivot: {x: 0, y: 1}
--- !u!1001 &643587747525445282
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 839941890697044924, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: ownershipManager
      value: 
      objectReference: {fileID: 422035517}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.51
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.42
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2909927925135986431, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3458172625772760724, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_Name
      value: Player
      objectReference: {fileID: 0}
    - target: {fileID: 3458172625772760724, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: chips
      value: 
      objectReference: {fileID: 11400000, guid: 0abcdefc8dfa346cf9906e8f2927df5b, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: moveSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: selectedSkillsController
      value: 
      objectReference: {fileID: 442068747}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: availableSkills.Array.size
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[0]'
      value: 
      objectReference: {fileID: 11400000, guid: 642177c2e7bac41268835f700114c542, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[1]'
      value: 
      objectReference: {fileID: 11400000, guid: 642177c2e7bac41268835f700114c542, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[2]'
      value: 
      objectReference: {fileID: 11400000, guid: 642177c2e7bac41268835f700114c542, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[3]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[4]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[5]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[6]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[7]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[8]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[9]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[10]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[11]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[12]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[13]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[14]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[15]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[16]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[17]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    - target: {fileID: 3714621940028834753, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
      propertyPath: 'availableSkills.Array.data[18]'
      value: 
      objectReference: {fileID: 11400000, guid: 15beea84445294272aae30aaa8b243af, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 620a69ae3a0cd48788170e2b175a208a, type: 3}
--- !u!1001 &2843392791381059755
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1982258823466629427, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_Name
      value: BarController
      objectReference: {fileID: 0}
    - target: {fileID: 1982258823466629427, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalScale.x
      value: 4.04
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.67
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.15
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.97
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5257225819616373570, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7105280178395971308, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
      propertyPath: listOfSkillsUI
      value: 
      objectReference: {fileID: 1925685032}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6a48529c0e09343c3ac4c8e8ff93aab7, type: 3}
--- !u!1001 &2935867585171200626
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1706565854333908703, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_Name
      value: Enemy Fan
      objectReference: {fileID: 0}
    - target: {fileID: 1706565854333908703, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2628960156748613024, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: tilemap
      value: 
      objectReference: {fileID: 284434049}
    - target: {fileID: 2628960156748613024, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: combatController
      value: 
      objectReference: {fileID: 588108309}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalPosition.x
      value: 3.5
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.5
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5701780100149679708, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: fd05013e9ec044a7b9ed55b508ebf5c2, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 519420032}
  - {fileID: 619394802}
  - {fileID: 1458798063}
  - {fileID: 1243450177}
  - {fileID: 1889078192}
  - {fileID: 422035516}
  - {fileID: 643587747525445282}
  - {fileID: 391281145}
  - {fileID: 2843392791381059755}
  - {fileID: 588108310}
  - {fileID: 1536397632}
  - {fileID: 1231149129}
  - {fileID: 196792566}
  - {fileID: 1747802813}
  - {fileID: 1538425632}
  - {fileID: 1227174729}
  - {fileID: 618560519}
  - {fileID: 793913968}
  - {fileID: 442068748}
  - {fileID: 1896381811}
  - {fileID: 2935867585171200626}
  - {fileID: 1243022527}
