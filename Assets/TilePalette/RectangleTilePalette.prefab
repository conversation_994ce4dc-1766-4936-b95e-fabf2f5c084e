%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3472757909770084932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3371176319304860455}
  - component: {fileID: 8564547204148935379}
  - component: {fileID: 5612678842521380363}
  m_Layer: 0
  m_Name: Layer1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3371176319304860455
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3472757909770084932}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4495541354912143118}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1839735485 &8564547204148935379
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3472757909770084932}
  m_Enabled: 1
  m_Tiles:
  - first: {x: -1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 0, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 5
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  - first: {x: 2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 4
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      dummyAlignment: 0
      m_AllTileFlags: 1073741825
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 1
    m_Data: {fileID: 11400000, guid: 7b5c30e65830f46afb112e6d88bc1f99, type: 2}
  - m_RefCount: 1
    m_Data: {fileID: 11400000, guid: f0e1c572d0cc54c4f807655f8505528f, type: 2}
  - m_RefCount: 1
    m_Data: {fileID: 11400000, guid: 9eabb7b930b974b69bdc9a8efe2c12f2, type: 2}
  - m_RefCount: 1
    m_Data: {fileID: 11400000, guid: caae9609405ec417f808a3457635ece3, type: 2}
  - m_RefCount: 1
    m_Data: {fileID: 11400000, guid: 00b066c43b5334d8a8bf8b0f2df5bf1f, type: 2}
  - m_RefCount: 1
    m_Data: {fileID: 11400000, guid: 7e7bcf18f43d44069a700054a2bcc3cb, type: 2}
  m_TileSpriteArray:
  - m_RefCount: 2
    m_Data: {fileID: 7482667652216324306, guid: a65a294a0ca5b4330898726b93fa6e40, type: 3}
  - m_RefCount: 2
    m_Data: {fileID: 21300000, guid: 317c66d8d8ec74f03b2d0bd7f6699ae8, type: 3}
  - m_RefCount: 2
    m_Data: {fileID: 21300000, guid: 95325a1370c2c425d8bbdd21c3a375a3, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 6
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 1
    m_Data: {r: 1, g: 0.3, b: 0.3, a: 1}
  - m_RefCount: 1
    m_Data: {r: 0.9578586, g: 0.9782206, b: 0.990566, a: 1}
  - m_RefCount: 2
    m_Data: {r: 1, g: 1, b: 1, a: 1}
  - m_RefCount: 2
    m_Data: {r: 0, g: 0, b: 0, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: -1, y: 0, z: 0}
  m_Size: {x: 4, y: 2, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!483693784 &5612678842521380363
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3472757909770084932}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 0
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1 &6129267178945179907
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4495541354912143118}
  - component: {fileID: 1583902727720548470}
  m_Layer: 0
  m_Name: RectangleTilePalette
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4495541354912143118
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6129267178945179907}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3371176319304860455}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!156049354 &1583902727720548470
Grid:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6129267178945179907}
  m_Enabled: 1
  m_CellSize: {x: 1, y: 1, z: 0}
  m_CellGap: {x: 0, y: 0, z: 0}
  m_CellLayout: 0
  m_CellSwizzle: 0
--- !u!114 &2544017797186773539
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12395, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: Palette Settings
  m_EditorClassIdentifier: 
  cellSizing: 0
  m_TransparencySortMode: 0
  m_TransparencySortAxis: {x: 0, y: 0, z: 1}
