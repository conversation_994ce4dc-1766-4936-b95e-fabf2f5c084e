{"MonoBehaviour": {"m_Enabled": true, "m_EditorHideFlags": 0, "m_Name": "", "m_EditorClassIdentifier": "", "ToolbarIndex": 0, "m_iOSNotificationSettingsValues": {"m_Keys": ["UnityNotificationRequestAuthorizationOnAppLaunch", "UnityNotificationDefaultAuthorizationOptions", "UnityAddRemoteNotificationCapability", "UnityNotificationRequestAuthorizationForRemoteNotificationsOnAppLaunch", "UnityRemoteNotificationForegroundPresentationOptions", "UnityUseAPSReleaseEnvironment", "UnityUseLocationNotificationTrigger"], "m_Values": ["True", "7", "False", "False", "-1", "False", "False"]}, "m_AndroidNotificationSettingsValues": {"m_Keys": ["UnityNotificationAndroidRescheduleOnDeviceRestart", "UnityNotificationAndroidUseCustomActivity", "UnityNotificationAndroidCustomActivityString"], "m_Values": ["False", "False", "com.unity3d.player.UnityPlayerActivity"]}, "DrawableResources": []}}